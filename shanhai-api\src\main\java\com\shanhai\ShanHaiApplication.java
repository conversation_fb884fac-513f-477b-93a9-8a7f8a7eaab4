package com.shanhai;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@MapperScan(basePackages = {
    "com.shanhai.common.crawler.repository",
    "com.shanhai.service.mapper",
    "com.gitee.sunchenbin.mybatis.actable.dao.*"
})
@ComponentScan({
    "com.shanhai.api.controller",
    "com.shanhai.api.config",
    "com.shanhai.common.crawler.service",
    "com.shanhai.common.crawler.service.impl",
    "com.shanhai.common.crawler.utils",
    "com.shanhai.common.crawler.config",
    "com.shanhai.common.crawler.strategy",
    "com.shanhai.common.crawler.manager",
    "com.gitee.sunchenbin.mybatis.actable.manager.*"
})
public class ShanHaiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ShanHaiApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  山海启动成功   ლ(´ڡ`ლ)");
    }

}