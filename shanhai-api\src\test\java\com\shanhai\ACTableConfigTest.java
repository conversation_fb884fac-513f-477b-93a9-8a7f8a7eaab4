package com.shanhai;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * ACTable 配置测试
 * 
 * 验证 ACTable 配置是否正确加载
 */
@SpringBootTest
@ActiveProfiles("dev")
public class ACTableConfigTest {

    @Value("${mybatis.table.auto:not-found}")
    private String tableAuto;

    @Value("${mybatis.model.pack:not-found}")
    private String modelPack;

    @Test
    public void testACTableConfig() {
        System.out.println("=== ACTable 配置测试 ===");
        
        System.out.println("mybatis.table.auto: " + tableAuto);
        System.out.println("mybatis.model.pack: " + modelPack);
        
        if ("update".equals(tableAuto)) {
            System.out.println("✅ ACTable 自动建表模式配置正确");
        } else {
            System.out.println("❌ ACTable 自动建表模式配置错误，当前值: " + tableAuto);
        }
        
        if (modelPack.contains("com.shanhai.common.crawler.model.config")) {
            System.out.println("✅ ACTable 实体类包路径配置正确");
        } else {
            System.out.println("❌ ACTable 实体类包路径配置错误，当前值: " + modelPack);
        }
        
        System.out.println("=== 配置测试完成 ===");
    }

    @Test
    public void testExpectedTableMode() {
        System.out.println("=== ACTable 支持的模式 ===");
        System.out.println("- none: 不执行任何操作");
        System.out.println("- update: 更新表结构（推荐，不会删除数据）");
        System.out.println("- create: 每次启动都重新创建表（会删除原有数据）");
        System.out.println("- add: 只新增字段和表，不删除和修改");
        System.out.println();
        System.out.println("当前配置模式: " + tableAuto);
        
        String[] supportedModes = {"none", "update", "create", "add"};
        boolean isSupported = false;
        for (String mode : supportedModes) {
            if (mode.equals(tableAuto)) {
                isSupported = true;
                break;
            }
        }
        
        if (isSupported) {
            System.out.println("✅ 当前配置模式受支持");
        } else {
            System.out.println("❌ 当前配置模式不受支持，请检查配置");
        }
    }
}
