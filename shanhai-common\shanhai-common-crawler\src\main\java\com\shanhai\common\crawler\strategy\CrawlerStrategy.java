package com.shanhai.common.crawler.strategy;

import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;

import java.util.List;
import java.util.Map;

/**
 * 爬虫采集策略接口
 * <p>
 * 统一管理不同的采集方式（API、HTML等）。
 * 实现类需保证线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface CrawlerStrategy {

    /**
     * 搜索书籍
     *
     * @param config  爬虫规则配置
     * @param keyword 搜索关键词
     * @return 匹配的书籍列表
     * @throws Exception 采集异常
     */
    List<NovelBook> searchBooks(CrawlerRuleNovel config, String keyword) throws Exception;

    /**
     * 获取书籍详情
     *
     * @param config  爬虫规则配置
     * @param bookUrl 书籍详情页URL
     * @return 书籍详情对象
     * @throws Exception 采集异常
     */
    NovelBook getBookInfo(CrawlerRuleNovel config, String bookUrl) throws Exception;

    /**
     * 获取章节列表
     *
     * @param config         爬虫规则配置
     * @param chapterListUrl 章节目录页URL
     * @return 章节列表
     * @throws Exception 采集异常
     */
    List<NovelBook.NovelChapter> getChapterList(CrawlerRuleNovel config, String chapterListUrl) throws Exception;

    /**
     * 获取章节内容
     *
     * @param config     爬虫规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容对象
     * @throws Exception 采集异常
     */
    NovelBook.NovelChapter getChapterContent(CrawlerRuleNovel config, String chapterUrl) throws Exception;

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    default String getStrategyName() {
        return this.getClass().getSimpleName().replace("CrawlerStrategy", "").toUpperCase();
    }

    /**
     * 获取策略类型
     *
     * @return 策略类型
     */
    default StrategyType getStrategyType() {
        return StrategyType.HTML;
    }

    /**
     * 检查策略是否支持指定的规则
     *
     * @param config 爬虫规则配置
     * @return 是否支持
     */
    default boolean supports(CrawlerRuleNovel config) {
        return true;
    }

    /**
     * 获取策略描述
     *
     * @return 策略描述
     */
    default String getDescription() {
        return "通用爬虫策略";
    }

    /**
     * 获取策略优先级（数值越小优先级越高）
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取策略配置要求
     *
     * @return 配置要求映射
     */
    default Map<String, Object> getConfigRequirements() {
        return Map.of();
    }

    /**
     * 验证配置是否满足策略要求
     *
     * @param config 爬虫规则配置
     * @return 验证结果
     */
    default ValidationResult validateConfig(CrawlerRuleNovel config) {
        return ValidationResult.success();
    }

    /**
     * 策略类型枚举
     */
    enum StrategyType {
        HTML("HTML解析", "通过HTML DOM解析网页内容"),
        API("API接口", "通过API接口获取JSON数据"),
        SELENIUM("浏览器模拟", "使用Selenium模拟浏览器操作"),
        HYBRID("混合模式", "结合多种采集方式");

        private final String displayName;
        private final String description;

        StrategyType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 验证结果类
     */
    class ValidationResult {
        private final boolean valid;
        private final String message;

        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, "验证通过");
        }

        public static ValidationResult failure(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}