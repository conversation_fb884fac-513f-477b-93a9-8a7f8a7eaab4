package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import java.io.Serializable;

/**
 * 章节列表页解析配置
 * <p>
 * 用于描述如何从章节列表页提取章节信息。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_chapter", comment = "章节列表页解析配置表")
@TableName("crawler_rule_chapter")
public class CrawlerRuleChapter implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 章节列表选择器
     */
    @Column(value = "chapter_list_selector", comment = "章节列表选择器", length = 200)
    private String chapterListSelector;

    /**
     * 单个章节项选择器
     */
    private String chapterItemSelector;

    /**
     * 章节标题选择器
     */
    private String titleSelector;

    /**
     * 章节链接选择器
     */
    private String urlSelector;

    /**
     * 章节链接属性
     */
    private String urlAttr;

    /**
     * 章节发布时间选择器
     */
    private String publishTimeSelector;

    /**
     * 是否反向排序
     */
    @Builder.Default
    private Boolean reverseOrder = false;

    /**
     * 下一页选择器
     */
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    private String nextPageAttr;

    /**
     * 是否启用分页
     */
    @Builder.Default
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    @Builder.Default
    private Integer maxPages = 50;

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    private Integer waitTime = 3000;

    private String ruleId;

    // 兼容旧代码的方法
    public String getChapterNameSelector() {
        return this.titleSelector;
    }

    public void setChapterNameSelector(String chapterNameSelector) {
        this.titleSelector = chapterNameSelector;
    }

    public String getChapterUrlSelector() {
        return this.urlSelector;
    }

    public void setChapterUrlSelector(String chapterUrlSelector) {
        this.urlSelector = chapterUrlSelector;
    }

    public String getChapterUrlAttr() {
        return this.urlAttr;
    }

    public void setChapterUrlAttr(String chapterUrlAttr) {
        this.urlAttr = chapterUrlAttr;
    }
}
