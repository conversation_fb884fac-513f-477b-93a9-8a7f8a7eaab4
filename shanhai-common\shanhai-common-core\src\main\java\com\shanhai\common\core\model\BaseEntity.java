package com.shanhai.common.core.model;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tangzc.autotable.annotation.mysql.MysqlTypeConstant;
import com.tangzc.mpe.autotable.annotation.Column;
import com.tangzc.mpe.autotable.annotation.ColumnId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseEntity extends Model<BaseEntity> {

    private static final long serialVersionUID = 1L;

    @ColumnId(value = "id", type = MysqlTypeConstant.VARCHAR, length = 32, comment = "ID(主键)")
    private String id;

    @Column(value = "create_user", length = 32, comment = "创建人")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(value = "create_time", comment = "创建时间")
    private Date createTime;

    @Column(value = "update_user", length = 32, comment = "修改人")
    private String updateUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(value = "update_time", comment = "修改时间")
    private Date updateTime;

    @Version
    @Column(value = "version", length = 7, comment = "版本")
    private Integer version = 0;

    @TableLogic(value = "0", delval = "1")
    @Column(value = "deleted", length = 1, comment = "删除标识")
    private Integer deleted = 0;

    @Column(value = "remark", length = 100, comment = "备注")
    private String remark;
}
