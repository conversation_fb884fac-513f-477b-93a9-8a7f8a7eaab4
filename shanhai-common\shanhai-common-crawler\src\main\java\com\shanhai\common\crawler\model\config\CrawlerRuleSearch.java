package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;

import java.io.Serializable;
import lombok.*;

/**
 * 搜索页解析配置
 * <p>
 * 用于描述如何从搜索页提取书籍列表及相关参数。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_search", comment = "搜索页解析配置表")
@TableName("crawler_rule_search")
public class CrawlerRuleSearch implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 搜索接口URL
     */
    @Column(value = "url", comment = "搜索接口URL", length = 500, notNull = true)
    private String url;

    /**
     * 搜索参数模板
     */
    @Column(value = "search_param", comment = "搜索参数模板", length = 500)
    private String searchParam;

    /**
     * 书籍列表选择器
     */
    @Column(value = "book_list_selector", comment = "书籍列表选择器", length = 200)
    private String bookListSelector;

    /**
     * 单本书籍项选择器
     */
    @Column(value = "book_item_selector", comment = "单本书籍项选择器", length = 200)
    private String bookItemSelector;

    /**
     * 书名选择器（CSS/JQ/JSONPath）
     */
    @Column(value = "name_selector", comment = "书名选择器", length = 200)
    private String nameSelector;

    /**
     * 作者选择器
     */
    @Column(value = "author_selector", comment = "作者选择器", length = 200)
    private String authorSelector;

    /**
     * 封面图片选择器
     */
    @Column(value = "cover_selector", comment = "封面图片选择器", length = 200)
    private String coverSelector;

    /**
     * 书籍详情页链接选择器
     */
    @Column(value = "detail_url_selector", comment = "书籍详情页链接选择器", length = 200)
    private String detailUrlSelector;

    /**
     * 书籍详情页链接属性
     */
    @Column(value = "detail_url_attr", comment = "书籍详情页链接属性", length = 50)
    private String detailUrlAttr;

    /**
     * 书籍URL选择器（兼容旧代码）
     */
    @Column(value = "book_url_selector", comment = "书籍URL选择器", length = 200)
    private String bookUrlSelector;

    /**
     * 书籍URL属性（兼容旧代码）
     */
    @Column(value = "book_url_attr", comment = "书籍URL属性", length = 50)
    private String bookUrlAttr;

    /**
     * 最新章节选择器
     */
    @Column(value = "latest_chapter_selector", comment = "最新章节选择器", length = 200)
    private String latestChapterSelector;

    /**
     * 书籍状态选择器
     */
    @Column(value = "status_selector", comment = "书籍状态选择器", length = 200)
    private String statusSelector;

    /**
     * 书籍分类选择器
     */
    @Column(value = "category_selector", comment = "书籍分类选择器", length = 200)
    private String categorySelector;

    /**
     * 书籍简介选择器
     */
    @Column(value = "intro_selector", comment = "书籍简介选择器", length = 200)
    private String introSelector;

    /**
     * 字数选择器
     */
    @Column(value = "word_count_selector", comment = "字数选择器", length = 200)
    private String wordCountSelector;

    /**
     * 更新时间选择器
     */
    @Column(value = "update_time_selector", comment = "更新时间选择器", length = 200)
    private String updateTimeSelector;

    /**
     * 下一页选择器
     */
    @Column(value = "next_page_selector", comment = "下一页选择器", length = 200)
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    @Column(value = "next_page_attr", comment = "下一页链接属性", length = 50)
    private String nextPageAttr;

    /**
     * 是否启用分页
     */
    @Builder.Default
    @Column(value = "enable_pagination", comment = "是否启用分页")
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    @Builder.Default
    @Column(value = "max_pages", comment = "最大页数限制")
    private Integer maxPages = 10;

    /**
     * 请求方法（GET/POST）
     */
    @Builder.Default
    @Column(value = "method", comment = "请求方法", length = 10)
    private String method = "GET";

    /**
     * 请求头配置（JSON格式）
     */
    @Column(value = "headers", comment = "请求头配置(JSON格式)", type = "TEXT")
    private String headers;

    /**
     * 请求体模板（POST请求时使用）
     */
    @Column(value = "request_body", comment = "请求体模板", type = "TEXT")
    private String requestBody;

    /**
     * 编码格式
     */
    @Builder.Default
    @Column(value = "charset", comment = "编码格式", length = 20)
    private String charset = "UTF-8";

    /**
     * 等待加载的选择器（动态页面）
     */
    @Column(value = "wait_for_selector", comment = "等待加载的选择器", length = 200)
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    @Column(value = "wait_time", comment = "等待时间(毫秒)")
    private Integer waitTime = 3000;

    @Column(value = "rule_id", comment = "关联规则ID", length = 32)
    private String ruleId;
}
