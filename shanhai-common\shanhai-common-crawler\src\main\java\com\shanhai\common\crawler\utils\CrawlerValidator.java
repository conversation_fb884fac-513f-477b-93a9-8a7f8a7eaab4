package com.shanhai.common.crawler.utils;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import com.shanhai.common.core.utils.StringUtils;
import com.shanhai.common.core.utils.ValidateUtils;
import org.springframework.stereotype.Component;

/**
 * 爬虫参数验证器
 * <p>
 * 提供统一的参数验证功能，确保爬虫配置和请求参数的有效性
 *
 * <AUTHOR>
 */
@Component
public class CrawlerValidator {

    /**
     * 验证爬虫规则配置
     */
    public void validateRule(NovelCrawlerRule rule) {
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }

        // 验证基本信息
        if (StringUtils.isBlank(rule.getSourceName())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点名称不能为空");
        }

        if (StringUtils.isBlank(rule.getSourceUrl())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点URL不能为空");
        }

        if (!ValidateUtils.isUrl(rule.getSourceUrl())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点URL格式不正确");
        }

        if (StringUtils.isBlank(rule.getMode())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "采集模式不能为空");
        }

        // 验证搜索配置
        if (rule.getRuleSearch() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索配置不能为空");
        }

        validateSearchRule(rule.getRuleSearch());

        // 验证书籍详情配置
        if (rule.getRuleBookInfo() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "书籍详情配置不能为空");
        }

        // 验证章节配置
        if (rule.getRuleChapter() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "章节配置不能为空");
        }

        // 验证内容配置
        if (rule.getRuleContent() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "内容配置不能为空");
        }
    }

    /**
     * 验证搜索规则
     */
    private void validateSearchRule(com.shanhai.common.crawler.model.config.RuleSearch ruleSearch) {
        if (StringUtils.isBlank(ruleSearch.getUrl())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索URL不能为空");
        }

        if (StringUtils.isBlank(ruleSearch.getBookListSelector())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "书籍列表选择器不能为空");
        }

        if (StringUtils.isBlank(ruleSearch.getNameSelector())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "书名选择器不能为空");
        }

        if (StringUtils.isBlank(ruleSearch.getBookUrlSelector())) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "书籍URL选择器不能为空");
        }
    }

    /**
     * 验证搜索关键词
     */
    public void validateKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索关键词不能为空");
        }

        if (keyword.trim().length() < 2) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索关键词至少需要2个字符");
        }

        if (keyword.length() > 50) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索关键词不能超过50个字符");
        }
    }

    /**
     * 验证URL
     */
    public void validateUrl(String url, String paramName) {
        if (StringUtils.isBlank(url)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "不能为空");
        }

        if (!ValidateUtils.isUrl(url)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "格式不正确");
        }
    }

    /**
     * 验证站点名称
     */
    public void validateSourceName(String sourceName) {
        if (StringUtils.isBlank(sourceName)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点名称不能为空");
        }

        if (sourceName.length() > 20) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点名称不能超过20个字符");
        }
    }

    /**
     * 验证批量URL列表
     */
    public void validateUrlList(java.util.List<String> urls, String paramName) {
        if (urls == null || urls.isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "列表不能为空");
        }

        if (urls.size() > 100) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "列表不能超过100个");
        }

        for (int i = 0; i < urls.size(); i++) {
            String url = urls.get(i);
            if (StringUtils.isBlank(url)) {
                throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                    paramName + "列表第" + (i + 1) + "个URL不能为空");
            }

            if (!ValidateUtils.isUrl(url)) {
                throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, 
                    paramName + "列表第" + (i + 1) + "个URL格式不正确");
            }
        }
    }

    /**
     * 验证CSS选择器
     *
     * @param selector 选择器字符串
     * @param paramName 参数名称，用于错误提示
     * @throws CrawlerException 选择器为空时抛出
     */
    public void validateSelector(String selector, String paramName) {
        if (StringUtils.isBlank(selector)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, paramName + "选择器不能为空");
        }
        // 注意：这里只做基本的空值检查，具体的CSS选择器语法验证由Jsoup在运行时处理
    }

    /**
     * 验证超时时间
     *
     * @param timeout 超时时间（毫秒），可为null
     * @throws CrawlerException 超时时间不在有效范围内时抛出
     */
    public void validateTimeout(Integer timeout) {
        if (timeout == null) {
            return;
        }

        final int MIN_TIMEOUT = 1000;  // 最小1秒
        final int MAX_TIMEOUT = 60000; // 最大60秒

        if (timeout < MIN_TIMEOUT) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER,
                String.format("超时时间不能小于%d毫秒", MIN_TIMEOUT));
        }

        if (timeout > MAX_TIMEOUT) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER,
                String.format("超时时间不能大于%d毫秒", MAX_TIMEOUT));
        }
    }
}
