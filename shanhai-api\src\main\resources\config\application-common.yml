# 通用配置文件
# 包含所有环境共享的配置项

# 应用信息
info:
  name: shanhai-novel
  version: 1.0.1
  description: 山海小说爬虫系统
  author: shanhai
  contact: 
    email: <EMAIL>
    website: https://shanhai.com

# 应用基础配置
server:
  # 优雅关闭
  shutdown: graceful
  # 连接超时时间
  connection-timeout: 20000
  # 请求头最大大小
  max-http-header-size: 8KB
  # 压缩配置
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Spring 通用配置
spring:
  # 应用名称
  application:
    name: shanhai-novel-crawler
  
  # 热部署配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
      exclude: static/**,public/**
  
  # Jackson 配置
  jackson:
    # 日期格式
    date-format: yyyy-MM-dd HH:mm:ss
    # 时区
    time-zone: GMT+8
    # 序列化配置
    serialization:
      # 格式化输出
      indent-output: false
      # 忽略空值
      write-null-map-values: false
    # 反序列化配置
    deserialization:
      # 忽略未知属性
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      # 最大文件大小
      max-file-size: 10MB
      # 最大请求大小
      max-request-size: 50MB
      # 文件大小阈值
      file-size-threshold: 2KB

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: false

# 爬虫通用配置
crawler:
  # 线程池配置
  thread-pool:
    core-size: 5
    max-size: 20
    queue-capacity: 100
    keep-alive-seconds: 60
  
  # 网络配置
  network:
    # 连接超时（毫秒）
    connect-timeout: 10000
    # 读取超时（毫秒）
    read-timeout: 30000
    # 最大重试次数
    max-retries: 3
    # 重试间隔（毫秒）
    retry-interval: 1000
  
  # 缓存配置
  cache:
    # 是否启用缓存
    enabled: true
    # 缓存大小
    max-size: 1000
    # 缓存过期时间（秒）
    expire-after-write: 3600
  
  # 限流配置
  rate-limit:
    # 是否启用限流
    enabled: true
    # 每秒请求数
    permits-per-second: 10
    # 突发请求数
    burst-capacity: 20
