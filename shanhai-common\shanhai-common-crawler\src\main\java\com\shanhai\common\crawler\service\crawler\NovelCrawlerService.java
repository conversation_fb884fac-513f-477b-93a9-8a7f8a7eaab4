package com.shanhai.common.crawler.service.crawler;

import com.shanhai.common.crawler.exception.ServiceException;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;

import java.util.List;

/**
 * 小说爬虫主流程服务接口
 * <p>
 * 仅定义主流程接口，参数校验请在 Controller 或工具类中完成。
 * 实现类请参考 NovelCrawlerServiceImpl。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface NovelCrawlerService {
    /**
     * 搜索小说书籍
     *
     * @param config  爬虫规则配置
     * @param keyword 搜索关键词
     * @return 匹配的书籍列表
     * @throws ServiceException 业务异常
     */
    List<NovelBook> searchBooks(NovelCrawlerRule config, String keyword);

    /**
     * 获取书籍详情
     *
     * @param config  爬虫规则配置
     * @param bookUrl 书籍详情页URL
     * @return 书籍详情对象
     * @throws ServiceException 业务异常
     */
    NovelBook getBookInfo(NovelCrawlerRule config, String bookUrl);

    /**
     * 获取章节列表
     *
     * @param config         爬虫规则配置
     * @param chapterListUrl 章节目录页URL
     * @return 章节列表
     * @throws ServiceException 业务异常
     */
    List<NovelBook.NovelChapter> getChapterList(NovelCrawlerRule config, String chapterListUrl);

    /**
     * 获取单个章节内容
     *
     * @param config     爬虫规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容对象
     * @throws ServiceException 业务异常
     */
    NovelBook.NovelChapter getChapterContent(NovelCrawlerRule config, String chapterUrl);

    /**
     * 批量获取章节内容
     *
     * @param config      爬虫规则配置
     * @param chapterUrls 章节内容页URL列表
     * @return 章节内容对象列表
     * @throws ServiceException 业务异常
     */
    List<NovelBook.NovelChapter> getBatchChapterContent(NovelCrawlerRule config, List<String> chapterUrls);
} 