package com.shanhai.common.crawler.strategy;

import cn.hutool.core.util.StrUtil;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 增强的爬虫策略工厂
 * <p>
 * 支持动态策略注册、自动发现、优先级排序等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EnhancedCrawlerStrategyFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    // 策略缓存：策略名称 -> 策略实例
    private final Map<String, CrawlerStrategy> strategyCache = new ConcurrentHashMap<>();
    
    // 策略类型映射：策略类型 -> 策略列表（按优先级排序）
    private final Map<CrawlerStrategy.StrategyType, List<CrawlerStrategy>> typeStrategies = new ConcurrentHashMap<>();
    
    // 支持的模式常量
    public static final String MODE_HTML = "HTML";
    public static final String MODE_API = "API";
    public static final String MODE_SELENIUM = "SELENIUM";
    public static final String MODE_HYBRID = "HYBRID";
    public static final String MODE_AUTO = "AUTO";
    
    @PostConstruct
    public void init() {
        // 自动发现并注册所有策略
        discoverAndRegisterStrategies();
        
        // 手动注册默认策略（如果Spring没有管理）
        registerDefaultStrategies();
        
        log.info("策略工厂初始化完成，共注册{}个策略", strategyCache.size());
        logRegisteredStrategies();
    }
    
    /**
     * 自动发现Spring管理的策略Bean
     */
    private void discoverAndRegisterStrategies() {
        Map<String, CrawlerStrategy> strategyBeans = applicationContext.getBeansOfType(CrawlerStrategy.class);
        
        for (Map.Entry<String, CrawlerStrategy> entry : strategyBeans.entrySet()) {
            CrawlerStrategy strategy = entry.getValue();
            registerStrategy(strategy);
        }
    }
    
    /**
     * 注册默认策略
     */
    private void registerDefaultStrategies() {
        // 如果没有找到HTML策略，注册默认的
        if (!hasStrategyForType(CrawlerStrategy.StrategyType.HTML)) {
            registerStrategy(new HtmlCrawlerStrategy());
        }
        
        // 如果没有找到API策略，注册默认的
        if (!hasStrategyForType(CrawlerStrategy.StrategyType.API)) {
            registerStrategy(new ApiCrawlerStrategy());
        }
    }
    
    /**
     * 注册策略
     */
    public void registerStrategy(CrawlerStrategy strategy) {
        if (strategy == null) {
            log.warn("尝试注册空策略，已忽略");
            return;
        }
        
        String strategyName = strategy.getStrategyName();
        CrawlerStrategy.StrategyType strategyType = strategy.getStrategyType();
        
        // 添加到策略缓存
        strategyCache.put(strategyName, strategy);
        
        // 添加到类型映射并按优先级排序
        typeStrategies.computeIfAbsent(strategyType, k -> new ArrayList<>()).add(strategy);
        typeStrategies.get(strategyType).sort(Comparator.comparingInt(CrawlerStrategy::getPriority));
        
        log.debug("注册策略: {} (类型: {}, 优先级: {})", strategyName, strategyType, strategy.getPriority());
    }
    
    /**
     * 根据规则获取最佳策略
     */
    public CrawlerStrategy getStrategy(CrawlerRuleNovel rule) {
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }
        
        String mode = rule.getMode();
        if (StrUtil.isBlank(mode)) {
            log.debug("采集模式为空，使用自动模式");
            mode = MODE_AUTO;
        }
        
        mode = mode.toUpperCase();
        
        // 自动模式：智能选择最佳策略
        if (MODE_AUTO.equals(mode)) {
            return selectBestStrategy(rule);
        }
        
        // 指定模式：获取对应策略
        CrawlerStrategy strategy = strategyCache.get(mode);
        if (strategy == null) {
            throw new CrawlerException(CrawlerErrorCode.STRATEGY_NOT_FOUND,
                String.format("不支持的采集模式: %s，支持的模式: %s", mode, getSupportedModes()));
        }
        
        // 验证策略是否支持当前规则
        if (!strategy.supports(rule)) {
            throw new CrawlerException(CrawlerErrorCode.STRATEGY_NOT_SUPPORTED,
                String.format("策略 %s 不支持当前规则配置", strategy.getStrategyName()));
        }
        
        log.debug("使用指定策略: {} for {}", strategy.getStrategyName(), rule.getSourceName());
        return strategy;
    }
    
    /**
     * 智能选择最佳策略
     */
    private CrawlerStrategy selectBestStrategy(CrawlerRuleNovel rule) {
        // 1. 根据URL特征判断
        CrawlerStrategy.StrategyType preferredType = detectStrategyType(rule);
        
        // 2. 获取该类型的所有策略
        List<CrawlerStrategy> candidates = typeStrategies.get(preferredType);
        if (candidates == null || candidates.isEmpty()) {
            // 如果首选类型没有策略，尝试HTML类型
            candidates = typeStrategies.get(CrawlerStrategy.StrategyType.HTML);
        }
        
        if (candidates == null || candidates.isEmpty()) {
            throw new CrawlerException(CrawlerErrorCode.STRATEGY_NOT_FOUND, "没有可用的爬虫策略");
        }
        
        // 3. 选择第一个支持当前规则的策略（已按优先级排序）
        for (CrawlerStrategy strategy : candidates) {
            if (strategy.supports(rule)) {
                CrawlerStrategy.ValidationResult validation = strategy.validateConfig(rule);
                if (validation.isValid()) {
                    log.debug("自动选择策略: {} (类型: {}) for {}", 
                        strategy.getStrategyName(), preferredType, rule.getSourceName());
                    return strategy;
                }
            }
        }
        
        // 4. 如果没有找到合适的策略，返回默认策略
        CrawlerStrategy defaultStrategy = getDefaultStrategy();
        log.warn("未找到合适策略，使用默认策略: {} for {}", 
            defaultStrategy.getStrategyName(), rule.getSourceName());
        return defaultStrategy;
    }
    
    /**
     * 检测策略类型
     */
    private CrawlerStrategy.StrategyType detectStrategyType(CrawlerRuleNovel rule) {
        // 检查搜索URL特征
        if (rule.getCrawlerRuleSearch() != null && rule.getCrawlerRuleSearch().getUrl() != null) {
            String searchUrl = rule.getCrawlerRuleSearch().getUrl();
            
            // API特征检测
            if (searchUrl.contains("/api/") || 
                searchUrl.contains("json") || 
                searchUrl.contains("ajax") ||
                searchUrl.contains("rest/")) {
                return CrawlerStrategy.StrategyType.API;
            }
            
            // 需要JavaScript的特征
            if (searchUrl.contains("spa") || 
                searchUrl.contains("react") || 
                searchUrl.contains("vue") ||
                (rule.getCrawlerRuleSearch().getWaitForSelector() != null)) {
                return CrawlerStrategy.StrategyType.SELENIUM;
            }
        }
        
        // 检查是否需要混合模式
        boolean hasApiFeatures = hasApiFeatures(rule);
        boolean hasHtmlFeatures = hasHtmlFeatures(rule);
        
        if (hasApiFeatures && hasHtmlFeatures) {
            return CrawlerStrategy.StrategyType.HYBRID;
        } else if (hasApiFeatures) {
            return CrawlerStrategy.StrategyType.API;
        }
        
        // 默认使用HTML
        return CrawlerStrategy.StrategyType.HTML;
    }
    
    /**
     * 检查是否有API特征
     */
    private boolean hasApiFeatures(CrawlerRuleNovel rule) {
        // 检查各个规则配置中是否有JSON路径选择器
        return (rule.getCrawlerRuleSearch() != null && hasJsonSelectors(rule.getCrawlerRuleSearch())) ||
               (rule.getCrawlerRuleBookInfo() != null && hasJsonSelectors(rule.getCrawlerRuleBookInfo())) ||
               (rule.getCrawlerRuleChapter() != null && hasJsonSelectors(rule.getCrawlerRuleChapter())) ||
               (rule.getCrawlerRuleContent() != null && hasJsonSelectors(rule.getCrawlerRuleContent()));
    }
    
    /**
     * 检查是否有HTML特征
     */
    private boolean hasHtmlFeatures(CrawlerRuleNovel rule) {
        // 检查是否有CSS选择器
        return (rule.getCrawlerRuleSearch() != null && hasCssSelectors(rule.getCrawlerRuleSearch())) ||
               (rule.getCrawlerRuleBookInfo() != null && hasCssSelectors(rule.getCrawlerRuleBookInfo())) ||
               (rule.getCrawlerRuleChapter() != null && hasCssSelectors(rule.getCrawlerRuleChapter())) ||
               (rule.getCrawlerRuleContent() != null && hasCssSelectors(rule.getCrawlerRuleContent()));
    }
    
    /**
     * 检查对象是否包含JSON选择器
     */
    private boolean hasJsonSelectors(Object ruleConfig) {
        // 这里可以通过反射检查字段值是否包含JSON路径语法
        // 简化实现：检查字段值是否包含JSON路径特征
        return false; // 暂时返回false，可以根据实际需要实现
    }
    
    /**
     * 检查对象是否包含CSS选择器
     */
    private boolean hasCssSelectors(Object ruleConfig) {
        // 这里可以通过反射检查字段值是否包含CSS选择器语法
        // 简化实现：大部分情况下都有CSS选择器
        return true;
    }
    
    /**
     * 获取默认策略
     */
    private CrawlerStrategy getDefaultStrategy() {
        // 优先返回HTML策略
        List<CrawlerStrategy> htmlStrategies = typeStrategies.get(CrawlerStrategy.StrategyType.HTML);
        if (htmlStrategies != null && !htmlStrategies.isEmpty()) {
            return htmlStrategies.get(0);
        }
        
        // 如果没有HTML策略，返回任意一个策略
        return strategyCache.values().iterator().next();
    }
    
    /**
     * 检查是否有指定类型的策略
     */
    private boolean hasStrategyForType(CrawlerStrategy.StrategyType type) {
        List<CrawlerStrategy> strategies = typeStrategies.get(type);
        return strategies != null && !strategies.isEmpty();
    }
    
    /**
     * 获取支持的模式列表
     */
    public String getSupportedModes() {
        return String.join(", ", strategyCache.keySet());
    }
    
    /**
     * 获取所有策略信息
     */
    public List<StrategyInfo> getAllStrategies() {
        return strategyCache.values().stream()
            .map(strategy -> StrategyInfo.builder()
                .name(strategy.getStrategyName())
                .type(strategy.getStrategyType())
                .description(strategy.getDescription())
                .priority(strategy.getPriority())
                .configRequirements(strategy.getConfigRequirements())
                .build())
            .sorted(Comparator.comparing(StrategyInfo::getPriority))
            .collect(Collectors.toList());
    }
    
    /**
     * 移除策略
     */
    public boolean removeStrategy(String strategyName) {
        CrawlerStrategy removed = strategyCache.remove(strategyName);
        if (removed != null) {
            // 从类型映射中也移除
            List<CrawlerStrategy> typeList = typeStrategies.get(removed.getStrategyType());
            if (typeList != null) {
                typeList.remove(removed);
            }
            log.info("移除策略: {}", strategyName);
            return true;
        }
        return false;
    }
    
    /**
     * 记录已注册的策略
     */
    private void logRegisteredStrategies() {
        if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder("\n已注册的爬虫策略:\n");
            for (CrawlerStrategy.StrategyType type : CrawlerStrategy.StrategyType.values()) {
                List<CrawlerStrategy> strategies = typeStrategies.get(type);
                if (strategies != null && !strategies.isEmpty()) {
                    sb.append(String.format("  %s (%s):\n", type.getDisplayName(), type.name()));
                    for (CrawlerStrategy strategy : strategies) {
                        sb.append(String.format("    - %s (优先级: %d) - %s\n", 
                            strategy.getStrategyName(), strategy.getPriority(), strategy.getDescription()));
                    }
                }
            }
            log.info(sb.toString());
        }
    }
    
    /**
     * 策略信息类
     */
    @lombok.Builder
    @lombok.Data
    public static class StrategyInfo {
        private String name;
        private CrawlerStrategy.StrategyType type;
        private String description;
        private int priority;
        private Map<String, Object> configRequirements;
    }
}
