package com.shanhai.common.crawler.cache;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 爬虫缓存管理器
 * <p>
 * 提供多级缓存、自动过期、性能监控等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlerCacheManager {
    
    // ================== 缓存配置 ==================
    
    private static final long DEFAULT_TTL = 30 * 60 * 1000L; // 30分钟
    private static final long CLEANUP_INTERVAL = 5 * 60 * 1000L; // 5分钟清理一次
    private static final int MAX_CACHE_SIZE = 10000; // 最大缓存条目数
    
    // ================== 缓存存储 ==================
    
    // 主缓存：存储实际数据
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    // 访问统计
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong evictionCount = new AtomicLong(0);
    
    // 清理任务调度器
    private ScheduledExecutorService cleanupScheduler;
    
    @PostConstruct
    public void init() {
        // 启动定期清理任务
        cleanupScheduler = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "cache-cleanup"));
        
        cleanupScheduler.scheduleAtFixedRate(
            this::cleanupExpiredEntries, 
            CLEANUP_INTERVAL, 
            CLEANUP_INTERVAL, 
            TimeUnit.MILLISECONDS
        );
        
        log.info("缓存管理器初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        if (cleanupScheduler != null) {
            cleanupScheduler.shutdown();
        }
        cache.clear();
        log.info("缓存管理器已关闭");
    }
    
    // ================== 缓存操作 ==================
    
    /**
     * 设置缓存（使用默认TTL）
     */
    public void put(String key, Object value) {
        put(key, value, DEFAULT_TTL);
    }
    
    /**
     * 设置缓存（指定TTL）
     */
    public void put(String key, Object value, long ttlMs) {
        if (StrUtil.isBlank(key) || value == null) {
            return;
        }
        
        // 检查缓存大小限制
        if (cache.size() >= MAX_CACHE_SIZE) {
            evictOldestEntries();
        }
        
        long expireTime = System.currentTimeMillis() + ttlMs;
        CacheEntry entry = new CacheEntry(value, expireTime, System.currentTimeMillis());
        
        cache.put(key, entry);
        log.debug("缓存设置: {} (TTL: {}ms)", key, ttlMs);
    }
    
    /**
     * 获取缓存
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }
        
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            missCount.incrementAndGet();
            log.debug("缓存未命中: {}", key);
            return null;
        }
        
        // 检查是否过期
        if (entry.isExpired()) {
            cache.remove(key);
            missCount.incrementAndGet();
            log.debug("缓存已过期: {}", key);
            return null;
        }
        
        // 更新访问时间
        entry.updateAccessTime();
        hitCount.incrementAndGet();
        log.debug("缓存命中: {}", key);
        
        return (T) entry.getValue();
    }
    
    /**
     * 获取缓存，如果不存在则通过提供的函数计算并缓存
     */
    public <T> T getOrCompute(String key, CacheLoader<T> loader) {
        return getOrCompute(key, loader, DEFAULT_TTL);
    }
    
    /**
     * 获取缓存，如果不存在则通过提供的函数计算并缓存（指定TTL）
     */
    public <T> T getOrCompute(String key, CacheLoader<T> loader, long ttlMs) {
        T value = get(key);
        if (value != null) {
            return value;
        }
        
        try {
            value = loader.load(key);
            if (value != null) {
                put(key, value, ttlMs);
            }
            return value;
        } catch (Exception e) {
            log.warn("缓存加载失败: {} - {}", key, e.getMessage());
            return null;
        }
    }
    
    /**
     * 删除缓存
     */
    public void remove(String key) {
        if (StrUtil.isNotBlank(key)) {
            cache.remove(key);
            log.debug("缓存删除: {}", key);
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void clear() {
        cache.clear();
        resetStats();
        log.info("缓存已清空");
    }
    
    /**
     * 检查缓存是否存在且未过期
     */
    public boolean exists(String key) {
        if (StrUtil.isBlank(key)) {
            return false;
        }
        
        CacheEntry entry = cache.get(key);
        return entry != null && !entry.isExpired();
    }
    
    // ================== 缓存管理 ==================
    
    /**
     * 清理过期条目
     */
    private void cleanupExpiredEntries() {
        long now = System.currentTimeMillis();
        int removedCount = 0;
        
        for (Map.Entry<String, CacheEntry> entry : cache.entrySet()) {
            if (entry.getValue().isExpired(now)) {
                cache.remove(entry.getKey());
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            evictionCount.addAndGet(removedCount);
            log.debug("清理过期缓存: {}个", removedCount);
        }
    }
    
    /**
     * 驱逐最旧的条目
     */
    private void evictOldestEntries() {
        int targetSize = MAX_CACHE_SIZE * 3 / 4; // 驱逐到75%容量
        int toRemove = cache.size() - targetSize;
        
        if (toRemove <= 0) {
            return;
        }
        
        // 按访问时间排序，移除最旧的条目
        cache.entrySet().stream()
            .sorted((e1, e2) -> Long.compare(e1.getValue().getLastAccessTime(), e2.getValue().getLastAccessTime()))
            .limit(toRemove)
            .forEach(entry -> cache.remove(entry.getKey()));
        
        evictionCount.addAndGet(toRemove);
        log.debug("驱逐旧缓存: {}个", toRemove);
    }
    
    // ================== 统计信息 ==================
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total * 100 : 0.0;
        
        return CacheStats.builder()
            .size(cache.size())
            .maxSize(MAX_CACHE_SIZE)
            .hitCount(hits)
            .missCount(misses)
            .hitRate(hitRate)
            .evictionCount(evictionCount.get())
            .build();
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        hitCount.set(0);
        missCount.set(0);
        evictionCount.set(0);
        log.info("缓存统计信息已重置");
    }
    
    /**
     * 获取缓存详细信息
     */
    public Map<String, Object> getCacheDetails() {
        Map<String, Object> details = new ConcurrentHashMap<>();
        
        for (Map.Entry<String, CacheEntry> entry : cache.entrySet()) {
            CacheEntry cacheEntry = entry.getValue();
            Map<String, Object> entryInfo = Map.of(
                "value", cacheEntry.getValue().getClass().getSimpleName(),
                "createTime", cacheEntry.getCreateTime(),
                "expireTime", cacheEntry.getExpireTime(),
                "lastAccessTime", cacheEntry.getLastAccessTime(),
                "expired", cacheEntry.isExpired()
            );
            details.put(entry.getKey(), entryInfo);
        }
        
        return details;
    }
    
    // ================== 内部类 ==================
    
    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final Object value;
        private final long expireTime;
        private final long createTime;
        private volatile long lastAccessTime;
        
        public CacheEntry(Object value, long expireTime, long createTime) {
            this.value = value;
            this.expireTime = expireTime;
            this.createTime = createTime;
            this.lastAccessTime = createTime;
        }
        
        public Object getValue() {
            return value;
        }
        
        public long getExpireTime() {
            return expireTime;
        }
        
        public long getCreateTime() {
            return createTime;
        }
        
        public long getLastAccessTime() {
            return lastAccessTime;
        }
        
        public void updateAccessTime() {
            this.lastAccessTime = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }
        
        public boolean isExpired(long now) {
            return now > expireTime;
        }
    }
    
    /**
     * 缓存加载器接口
     */
    @FunctionalInterface
    public interface CacheLoader<T> {
        T load(String key) throws Exception;
    }
    
    /**
     * 缓存统计信息
     */
    @lombok.Builder
    @lombok.Data
    public static class CacheStats {
        private int size;
        private int maxSize;
        private long hitCount;
        private long missCount;
        private double hitRate;
        private long evictionCount;
        
        public String getFormattedStats() {
            return String.format(
                "缓存统计 - 大小: %d/%d, 命中: %d, 未命中: %d, 命中率: %.2f%%, 驱逐: %d",
                size, maxSize, hitCount, missCount, hitRate, evictionCount
            );
        }
    }
}
