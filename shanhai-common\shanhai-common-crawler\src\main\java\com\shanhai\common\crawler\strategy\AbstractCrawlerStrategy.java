package com.shanhai.common.crawler.strategy;

import cn.hutool.core.util.StrUtil;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.network.HttpClientManager;
import com.shanhai.common.crawler.utils.CrawlerToolkit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 抽象爬虫策略基类
 * <p>
 * 提供通用的功能实现，减少子类重复代码
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCrawlerStrategy implements CrawlerStrategy {
    
    @Autowired
    protected HttpClientManager httpClientManager;
    
    // ================== 抽象方法 ==================
    
    /**
     * 子类实现具体的搜索逻辑
     */
    protected abstract List<NovelBook> doSearchBooks(CrawlerRuleNovel config, String keyword) throws Exception;
    
    /**
     * 子类实现具体的书籍详情获取逻辑
     */
    protected abstract NovelBook doGetBookInfo(CrawlerRuleNovel config, String bookUrl) throws Exception;
    
    /**
     * 子类实现具体的章节列表获取逻辑
     */
    protected abstract List<NovelBook.NovelChapter> doGetChapterList(CrawlerRuleNovel config, String chapterListUrl) throws Exception;
    
    /**
     * 子类实现具体的章节内容获取逻辑
     */
    protected abstract NovelBook.NovelChapter doGetChapterContent(CrawlerRuleNovel config, String chapterUrl) throws Exception;
    
    // ================== 模板方法实现 ==================
    
    @Override
    public final List<NovelBook> searchBooks(CrawlerRuleNovel config, String keyword) throws Exception {
        // 前置验证
        validateSearchRequest(config, keyword);
        
        // 生成追踪ID
        String traceId = generateTraceId();
        long startTime = System.currentTimeMillis();
        
        log.debug("[{}] 开始搜索书籍: {} - {}", traceId, keyword, getStrategyName());
        
        try {
            // 前置处理
            preProcessSearch(config, keyword);
            
            // 执行搜索
            List<NovelBook> results = doSearchBooks(config, keyword);
            
            // 后置处理
            results = postProcessSearchResults(config, keyword, results);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 搜索完成: 找到{}本书籍 ({}ms)", traceId, results.size(), duration);
            
            return results;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 搜索失败: {} ({}ms) - {}", traceId, keyword, duration, e.getMessage());
            throw convertException(e, "搜索书籍");
        }
    }
    
    @Override
    public final NovelBook getBookInfo(CrawlerRuleNovel config, String bookUrl) throws Exception {
        // 前置验证
        validateBookInfoRequest(config, bookUrl);
        
        String traceId = generateTraceId();
        long startTime = System.currentTimeMillis();
        
        log.debug("[{}] 开始获取书籍详情: {} - {}", traceId, bookUrl, getStrategyName());
        
        try {
            // 前置处理
            preProcessBookInfo(config, bookUrl);
            
            // 执行获取
            NovelBook result = doGetBookInfo(config, bookUrl);
            
            // 后置处理
            result = postProcessBookInfo(config, bookUrl, result);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 获取书籍详情完成: {} ({}ms)", traceId, result.getName(), duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 获取书籍详情失败: {} ({}ms) - {}", traceId, bookUrl, duration, e.getMessage());
            throw convertException(e, "获取书籍详情");
        }
    }
    
    @Override
    public final List<NovelBook.NovelChapter> getChapterList(CrawlerRuleNovel config, String chapterListUrl) throws Exception {
        // 前置验证
        validateChapterListRequest(config, chapterListUrl);
        
        String traceId = generateTraceId();
        long startTime = System.currentTimeMillis();
        
        log.debug("[{}] 开始获取章节列表: {} - {}", traceId, chapterListUrl, getStrategyName());
        
        try {
            // 前置处理
            preProcessChapterList(config, chapterListUrl);
            
            // 执行获取
            List<NovelBook.NovelChapter> results = doGetChapterList(config, chapterListUrl);
            
            // 后置处理
            results = postProcessChapterList(config, chapterListUrl, results);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 获取章节列表完成: {}章 ({}ms)", traceId, results.size(), duration);
            
            return results;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 获取章节列表失败: {} ({}ms) - {}", traceId, chapterListUrl, duration, e.getMessage());
            throw convertException(e, "获取章节列表");
        }
    }
    
    @Override
    public final NovelBook.NovelChapter getChapterContent(CrawlerRuleNovel config, String chapterUrl) throws Exception {
        // 前置验证
        validateChapterContentRequest(config, chapterUrl);
        
        String traceId = generateTraceId();
        long startTime = System.currentTimeMillis();
        
        log.debug("[{}] 开始获取章节内容: {} - {}", traceId, chapterUrl, getStrategyName());
        
        try {
            // 前置处理
            preProcessChapterContent(config, chapterUrl);
            
            // 执行获取
            NovelBook.NovelChapter result = doGetChapterContent(config, chapterUrl);
            
            // 后置处理
            result = postProcessChapterContent(config, chapterUrl, result);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 获取章节内容完成: {} ({}ms)", traceId, result.getTitle(), duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[{}] 获取章节内容失败: {} ({}ms) - {}", traceId, chapterUrl, duration, e.getMessage());
            throw convertException(e, "获取章节内容");
        }
    }
    
    // ================== 验证方法 ==================
    
    protected void validateSearchRequest(CrawlerRuleNovel config, String keyword) {
        CrawlerToolkit.validateRequired(config, "爬虫配置");
        CrawlerToolkit.validateRequired(keyword, "搜索关键词");
        
        if (config.getCrawlerRuleSearch() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "搜索规则配置不能为空");
        }
    }
    
    protected void validateBookInfoRequest(CrawlerRuleNovel config, String bookUrl) {
        CrawlerToolkit.validateRequired(config, "爬虫配置");
        CrawlerToolkit.validateUrl(bookUrl, "书籍详情页URL");
        
        if (config.getCrawlerRuleBookInfo() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "书籍详情规则配置不能为空");
        }
    }
    
    protected void validateChapterListRequest(CrawlerRuleNovel config, String chapterListUrl) {
        CrawlerToolkit.validateRequired(config, "爬虫配置");
        CrawlerToolkit.validateUrl(chapterListUrl, "章节列表页URL");
        
        if (config.getCrawlerRuleChapter() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "章节列表规则配置不能为空");
        }
    }
    
    protected void validateChapterContentRequest(CrawlerRuleNovel config, String chapterUrl) {
        CrawlerToolkit.validateRequired(config, "爬虫配置");
        CrawlerToolkit.validateUrl(chapterUrl, "章节内容页URL");
        
        if (config.getCrawlerRuleContent() == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "章节内容规则配置不能为空");
        }
    }
    
    // ================== 钩子方法（子类可选择性重写） ==================
    
    /**
     * 搜索前置处理
     */
    protected void preProcessSearch(CrawlerRuleNovel config, String keyword) {
        // 默认实现：应用反爬虫延迟
        applyAntiSpiderDelay(config);
    }
    
    /**
     * 搜索结果后置处理
     */
    protected List<NovelBook> postProcessSearchResults(CrawlerRuleNovel config, String keyword, List<NovelBook> results) {
        if (results == null) {
            return new ArrayList<>();
        }
        
        // 默认实现：清理和验证结果
        return results.stream()
            .filter(book -> book != null && StrUtil.isNotBlank(book.getName()))
            .peek(this::cleanBookInfo)
            .toList();
    }
    
    /**
     * 书籍详情前置处理
     */
    protected void preProcessBookInfo(CrawlerRuleNovel config, String bookUrl) {
        applyAntiSpiderDelay(config);
    }
    
    /**
     * 书籍详情后置处理
     */
    protected NovelBook postProcessBookInfo(CrawlerRuleNovel config, String bookUrl, NovelBook book) {
        if (book != null) {
            cleanBookInfo(book);
            // 设置书籍URL（如果没有设置）
            if (StrUtil.isBlank(book.getBookUrl())) {
                book.setBookUrl(bookUrl);
            }
        }
        return book;
    }
    
    /**
     * 章节列表前置处理
     */
    protected void preProcessChapterList(CrawlerRuleNovel config, String chapterListUrl) {
        applyAntiSpiderDelay(config);
    }
    
    /**
     * 章节列表后置处理
     */
    protected List<NovelBook.NovelChapter> postProcessChapterList(CrawlerRuleNovel config, String chapterListUrl, List<NovelBook.NovelChapter> chapters) {
        if (chapters == null) {
            return new ArrayList<>();
        }
        
        // 清理和验证章节
        return chapters.stream()
            .filter(chapter -> chapter != null && StrUtil.isNotBlank(chapter.getTitle()))
            .peek(this::cleanChapterInfo)
            .toList();
    }
    
    /**
     * 章节内容前置处理
     */
    protected void preProcessChapterContent(CrawlerRuleNovel config, String chapterUrl) {
        applyAntiSpiderDelay(config);
    }
    
    /**
     * 章节内容后置处理
     */
    protected NovelBook.NovelChapter postProcessChapterContent(CrawlerRuleNovel config, String chapterUrl, NovelBook.NovelChapter chapter) {
        if (chapter != null) {
            cleanChapterInfo(chapter);
            // 设置章节URL（如果没有设置）
            if (StrUtil.isBlank(chapter.getUrl())) {
                chapter.setUrl(chapterUrl);
            }
        }
        return chapter;
    }
    
    // ================== 工具方法 ==================
    
    /**
     * 应用反爬虫延迟
     */
    protected void applyAntiSpiderDelay(CrawlerRuleNovel config) {
        if (config.getCrawlerRuleAntiSpider() != null) {
            Integer minDelay = config.getCrawlerRuleAntiSpider().getMinDelayMs();
            Integer maxDelay = config.getCrawlerRuleAntiSpider().getMaxDelayMs();
            
            if (minDelay != null && maxDelay != null && minDelay > 0 && maxDelay > minDelay) {
                long delay = CrawlerToolkit.getRandomDelay(minDelay, maxDelay);
                CrawlerToolkit.safeSleep(delay);
            }
        }
    }
    
    /**
     * 清理书籍信息
     */
    protected void cleanBookInfo(NovelBook book) {
        if (book == null) return;
        
        book.setName(CrawlerToolkit.cleanText(book.getName()));
        book.setAuthor(CrawlerToolkit.cleanText(book.getAuthor()));
        book.setIntro(CrawlerToolkit.cleanText(book.getIntro()));
        book.setCategory(CrawlerToolkit.cleanText(book.getCategory()));
        book.setStatus(CrawlerToolkit.cleanText(book.getStatus()));
        book.setLastChapter(CrawlerToolkit.cleanText(book.getLastChapter()));
    }
    
    /**
     * 清理章节信息
     */
    protected void cleanChapterInfo(NovelBook.NovelChapter chapter) {
        if (chapter == null) return;
        
        chapter.setTitle(CrawlerToolkit.cleanText(chapter.getTitle()));
        if (StrUtil.isNotBlank(chapter.getContent())) {
            chapter.setContent(CrawlerToolkit.cleanChapterContent(chapter.getContent()));
        }
    }
    
    /**
     * 转换异常
     */
    protected CrawlerException convertException(Exception e, String operation) {
        if (e instanceof CrawlerException) {
            return (CrawlerException) e;
        }
        
        return new CrawlerException(CrawlerErrorCode.PARSE_ERROR, 
            String.format("%s异常: %s", operation, e.getMessage()), e);
    }
    
    /**
     * 生成追踪ID
     */
    protected String generateTraceId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
    
    // ================== 默认实现 ==================
    
    @Override
    public ValidationResult validateConfig(CrawlerRuleNovel config) {
        if (config == null) {
            return ValidationResult.failure("配置不能为空");
        }
        
        // 子类可以重写此方法提供更详细的验证
        return ValidationResult.success();
    }
    
    @Override
    public Map<String, Object> getConfigRequirements() {
        // 子类可以重写此方法提供配置要求
        return Map.of(
            "required_fields", List.of("sourceName", "sourceUrl", "mode"),
            "optional_fields", List.of("userAgent", "timeout", "headers", "cookies")
        );
    }
}
