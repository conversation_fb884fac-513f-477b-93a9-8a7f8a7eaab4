package com.shanhai.common.crawler.exception;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.concurrent.TimeoutException;

/**
 * 爬虫异常处理工具类
 * <p>
 * 提供统一的异常转换、处理和记录功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class CrawlerExceptionHandler {
    
    /**
     * 将通用异常转换为爬虫异常
     */
    public static CrawlerException convertException(Exception e, String operation) {
        if (e instanceof CrawlerException) {
            return (CrawlerException) e;
        }
        
        // 网络相关异常
        if (e instanceof SocketTimeoutException) {
            return new CrawlerException(CrawlerErrorCode.NETWORK_TIMEOUT, 
                String.format("%s超时: %s", operation, e.getMessage()), e);
        }
        
        if (e instanceof UnknownHostException) {
            return new CrawlerException(CrawlerErrorCode.DNS_RESOLUTION_FAILED, 
                String.format("%s DNS解析失败: %s", operation, e.getMessage()), e);
        }
        
        if (e instanceof IOException) {
            String message = e.getMessage();
            if (message != null) {
                if (message.contains("403")) {
                    return new CrawlerException(CrawlerErrorCode.ACCESS_DENIED, 
                        String.format("%s访问被拒绝: %s", operation, message), e);
                } else if (message.contains("404")) {
                    return new CrawlerException(CrawlerErrorCode.BOOK_NOT_FOUND, 
                        String.format("%s页面不存在: %s", operation, message), e);
                } else if (message.contains("429")) {
                    return new CrawlerException(CrawlerErrorCode.RATE_LIMIT_EXCEEDED, 
                        String.format("%s请求频率超限: %s", operation, message), e);
                } else if (message.contains("502") || message.contains("503") || message.contains("504")) {
                    return new CrawlerException(CrawlerErrorCode.SYSTEM_ERROR, 
                        String.format("%s服务器错误: %s", operation, message), e);
                }
            }
            return new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, 
                String.format("%s网络请求失败: %s", operation, message), e);
        }
        
        // 超时异常
        if (e instanceof TimeoutException) {
            return new CrawlerException(CrawlerErrorCode.NETWORK_TIMEOUT, 
                String.format("%s操作超时: %s", operation, e.getMessage()), e);
        }
        
        // 中断异常
        if (e instanceof InterruptedException) {
            return new CrawlerException(CrawlerErrorCode.THREAD_INTERRUPTED, 
                String.format("%s线程被中断: %s", operation, e.getMessage()), e);
        }
        
        // 其他异常
        return new CrawlerException(CrawlerErrorCode.SYSTEM_ERROR, 
            String.format("%s系统异常: %s", operation, e.getMessage()), e);
    }
    
    /**
     * 记录异常信息
     */
    public static void logException(CrawlerException e, String context) {
        CrawlerErrorCode errorCode = e.getErrorCode();
        String logMessage = String.format("[%s] %s - 错误码: %d, 分类: %s, 级别: %s", 
            context, e.getMessage(), errorCode.getCode(), 
            errorCode.getCategory().getDescription(), errorCode.getLevel().getDescription());
        
        switch (errorCode.getLevel()) {
            case INFO:
                log.info(logMessage);
                break;
            case WARNING:
                log.warn(logMessage, e);
                break;
            case ERROR:
                log.error(logMessage, e);
                break;
            case CRITICAL:
                log.error("【严重错误】" + logMessage, e);
                break;
        }
    }
    
    /**
     * 判断异常是否应该重试
     */
    public static boolean shouldRetry(Exception e, int currentAttempt, int maxAttempts) {
        if (currentAttempt >= maxAttempts) {
            return false;
        }
        
        if (e instanceof CrawlerException) {
            CrawlerException ce = (CrawlerException) e;
            return ce.getErrorCode().isRetryable() && currentAttempt < ce.getErrorCode().getMaxRetries();
        }
        
        // 对于非爬虫异常，根据异常类型判断
        return e instanceof SocketTimeoutException || 
               e instanceof IOException || 
               e instanceof TimeoutException;
    }
    
    /**
     * 获取重试延迟时间
     */
    public static long getRetryDelay(Exception e, int attempt) {
        long baseDelay = 1000L; // 默认1秒
        
        if (e instanceof CrawlerException) {
            CrawlerException ce = (CrawlerException) e;
            baseDelay = ce.getErrorCode().getRetryDelay();
        }
        
        // 指数退避策略
        return baseDelay * (1L << (attempt - 1));
    }
    
    /**
     * 创建详细的错误报告
     */
    public static ErrorReport createErrorReport(Exception e, String operation, String url) {
        CrawlerException ce = convertException(e, operation);
        
        return ErrorReport.builder()
            .errorCode(ce.getErrorCode())
            .operation(operation)
            .url(url)
            .message(ce.getMessage())
            .category(ce.getErrorCode().getCategory())
            .level(ce.getErrorCode().getLevel())
            .retryable(ce.getErrorCode().isRetryable())
            .maxRetries(ce.getErrorCode().getMaxRetries())
            .retryDelay(ce.getErrorCode().getRetryDelay())
            .timestamp(System.currentTimeMillis())
            .stackTrace(getStackTrace(e))
            .build();
    }
    
    /**
     * 获取异常堆栈信息
     */
    private static String getStackTrace(Exception e) {
        if (e == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(e.getClass().getSimpleName()).append(": ").append(e.getMessage()).append("\n");
        
        StackTraceElement[] elements = e.getStackTrace();
        int maxLines = Math.min(elements.length, 10); // 最多显示10行
        
        for (int i = 0; i < maxLines; i++) {
            sb.append("  at ").append(elements[i].toString()).append("\n");
        }
        
        if (elements.length > maxLines) {
            sb.append("  ... ").append(elements.length - maxLines).append(" more\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化异常信息用于用户显示
     */
    public static String formatUserMessage(CrawlerException e) {
        CrawlerErrorCode errorCode = e.getErrorCode();
        
        StringBuilder sb = new StringBuilder();
        sb.append("操作失败: ").append(errorCode.getMessage());
        
        // 根据错误类型提供用户友好的建议
        switch (errorCode.getCategory()) {
            case NETWORK:
                sb.append("\n建议: 检查网络连接或稍后重试");
                break;
            case ANTI_SPIDER:
                sb.append("\n建议: 降低访问频率或更换访问方式");
                break;
            case RULE_CONFIG:
                sb.append("\n建议: 检查爬虫规则配置是否正确");
                break;
            case PARSING:
                sb.append("\n建议: 网站结构可能已变更，需要更新解析规则");
                break;
            case BUSINESS:
                sb.append("\n建议: 检查目标内容是否存在");
                break;
            default:
                if (errorCode.isRetryable()) {
                    sb.append("\n建议: 稍后重试");
                }
                break;
        }
        
        return sb.toString();
    }
    
    /**
     * 错误报告类
     */
    @lombok.Builder
    @lombok.Data
    public static class ErrorReport {
        private CrawlerErrorCode errorCode;
        private String operation;
        private String url;
        private String message;
        private CrawlerErrorCode.ErrorCategory category;
        private CrawlerErrorCode.ErrorLevel level;
        private boolean retryable;
        private int maxRetries;
        private long retryDelay;
        private long timestamp;
        private String stackTrace;
        
        /**
         * 获取格式化的报告
         */
        public String getFormattedReport() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 爬虫异常报告 ===\n");
            sb.append("时间: ").append(new java.util.Date(timestamp)).append("\n");
            sb.append("操作: ").append(operation).append("\n");
            if (StrUtil.isNotBlank(url)) {
                sb.append("URL: ").append(url).append("\n");
            }
            sb.append("错误码: ").append(errorCode.getCode()).append("\n");
            sb.append("错误信息: ").append(message).append("\n");
            sb.append("错误分类: ").append(category.getDescription()).append("\n");
            sb.append("错误级别: ").append(level.getDescription()).append("\n");
            sb.append("可重试: ").append(retryable ? "是" : "否").append("\n");
            if (retryable) {
                sb.append("最大重试次数: ").append(maxRetries).append("\n");
                sb.append("重试延迟: ").append(retryDelay).append("ms\n");
            }
            sb.append("堆栈信息:\n").append(stackTrace);
            sb.append("==================\n");
            
            return sb.toString();
        }
    }
    
    // 私有构造函数，防止实例化
    private CrawlerExceptionHandler() {
        throw new UnsupportedOperationException("工具类不能实例化");
    }
}
