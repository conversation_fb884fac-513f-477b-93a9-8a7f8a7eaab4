package com.shanhai.api.exception;

import com.shanhai.common.core.result.R;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

/**
 * 全局异常处理器
 * 
 * 统一处理应用程序中的各种异常，提供友好的错误响应
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理爬虫异常
     */
    @ExceptionHandler(CrawlerException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleCrawlerException(CrawlerException e, HttpServletRequest request) {
        log.error("爬虫异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        return R.fail(e.getErrorCode().getCode(), e.getMessage());
    }
    
    /**
     * 处理服务异常
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleServiceException(ServiceException e, HttpServletRequest request) {
        log.error("服务异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        return R.fail(500, e.getMessage());
    }
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleValidationException(Exception e, HttpServletRequest request) {
        log.warn("参数验证异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage());
        
        String message = "参数验证失败";
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            if (ex.getBindingResult().hasFieldErrors()) {
                message = ex.getBindingResult().getFieldError().getDefaultMessage();
            }
        } else if (e instanceof BindException) {
            BindException ex = (BindException) e;
            if (ex.getBindingResult().hasFieldErrors()) {
                message = ex.getBindingResult().getFieldError().getDefaultMessage();
            }
        }
        
        return R.fail(400, message);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        log.warn("约束违反异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage());
        return R.fail(400, "参数约束违反: " + e.getMessage());
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        log.warn("非法参数异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage());
        return R.fail(400, "参数错误: " + e.getMessage());
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("运行时异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        return R.fail(500, "系统内部错误");
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleException(Exception e, HttpServletRequest request) {
        log.error("未知异常 - URL: {}, 错误: {}", request.getRequestURL(), e.getMessage(), e);
        return R.fail(500, "系统异常，请联系管理员");
    }
}
