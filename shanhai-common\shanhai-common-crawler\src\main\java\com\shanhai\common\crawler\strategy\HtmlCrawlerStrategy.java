package com.shanhai.common.crawler.strategy;


import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.model.config.*;
import com.shanhai.common.crawler.utils.CrawlerNetworkManager;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * HTML采集策略实现
 * <p>
 * 适用于静态网站，直接通过DOM解析采集书籍、详情、章节列表和章节内容。
 * 线程安全：无状态实现，线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class HtmlCrawlerStrategy implements CrawlerStrategy {
    /**
     * 最大等待异步内容时间（毫秒）
     */
    private static final int MAX_WAIT_TIME = 10000;

    public HtmlCrawlerStrategy() {
    }

    /**
     * 搜索书籍（HTML模式）
     *
     * @param config  采集规则配置
     * @param keyword 搜索关键词
     * @return 书籍列表
     * @throws CrawlerException 采集异常
     */
    @Override
    public List<NovelBook> searchBooks(CrawlerRuleNovel config, String keyword) throws CrawlerException {
        if (config == null || config.getCrawlerRuleSearch() == null) return new ArrayList<>();
        try {
            String searchUrl = buildSearchUrl(config.getCrawlerRuleSearch(), keyword);
            Document doc;
            try {
                doc = fetchSearchDocument(searchUrl, config);
            } catch (IOException ioe) {
                log.error("[HTML] 搜索书籍网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "HTML搜索书籍网络异常: " + ioe.getMessage());
            }
            waitForSearchAsyncContent(config);
            return parseBookListFromSearch(doc, config);
        } catch (Exception e) {
            log.error("[HTML] 搜索书籍异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "HTML搜索书籍异常: " + e.getMessage());
        }
    }

    /**
     * 获取书籍详情（HTML模式）
     *
     * @param config  采集规则配置
     * @param bookUrl 详情页URL
     * @return 书籍详情
     * @throws CrawlerException 采集异常
     */
    @Override
    public NovelBook getBookInfo(CrawlerRuleNovel config, String bookUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleBookInfo() == null) return new NovelBook();
        try {
            Document doc;
            try {
                doc = fetchBookInfoDocument(bookUrl, config);
            } catch (IOException ioe) {
                log.error("[HTML] 获取书籍详情网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "HTML获取书籍详情网络异常: " + ioe.getMessage());
            }
            waitForBookInfoAsyncContent(config);
            return parseBookInfoFromDocument(doc, config);
        } catch (Exception e) {
            log.error("[HTML] 获取书籍详情异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "HTML获取书籍详情异常: " + e.getMessage());
        }
    }

    private Document fetchBookInfoDocument(String bookUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(bookUrl, config);
    }

    private void waitForBookInfoAsyncContent(CrawlerRuleNovel config) {
        waitForAsyncContent(config.getCrawlerRuleBookInfo().getWaitForSelector(), MAX_WAIT_TIME);
    }

    private NovelBook parseBookInfoFromDocument(Document doc, CrawlerRuleNovel config) {
        // 这里不再调用fetchXXXDocument，不会抛出IOException
        return extractBookInfo(doc, config.getCrawlerRuleBookInfo());
    }

    /**
     * 获取章节列表（HTML模式）
     *
     * @param config         采集规则配置
     * @param chapterListUrl 章节列表页URL
     * @return 章节列表
     * @throws CrawlerException 采集异常
     */
    @Override
    public List<NovelBook.NovelChapter> getChapterList(CrawlerRuleNovel config, String chapterListUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleChapter() == null) return new ArrayList<>();
        try {
            Document doc;
            try {
                doc = fetchChapterListDocument(chapterListUrl, config);
            } catch (IOException ioe) {
                log.error("[HTML] 获取章节列表网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "HTML获取章节列表网络异常: " + ioe.getMessage());
            }
            waitForChapterListAsyncContent(config);
            return parseChapterListFromDocument(doc, config);
        } catch (Exception e) {
            log.error("[HTML] 获取章节列表异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "HTML获取章节列表异常: " + e.getMessage());
        }
    }

    private Document fetchChapterListDocument(String chapterListUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(chapterListUrl, config);
    }

    private void waitForChapterListAsyncContent(CrawlerRuleNovel config) {
        waitForAsyncContent(config.getCrawlerRuleChapter().getWaitForSelector(), MAX_WAIT_TIME);
    }

    private List<NovelBook.NovelChapter> parseChapterListFromDocument(Document doc, CrawlerRuleNovel config) {
        // 这里不再调用fetchXXXDocument，不会抛出IOException
        return extractChapterList(doc, config.getCrawlerRuleChapter());
    }

    /**
     * 获取章节内容（HTML模式）
     *
     * @param config     采集规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容
     * @throws CrawlerException 采集异常
     */
    @Override
    public NovelBook.NovelChapter getChapterContent(CrawlerRuleNovel config, String chapterUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleContent() == null) return new NovelBook.NovelChapter();
        try {
            Document doc;
            try {
                doc = fetchChapterContentDocument(chapterUrl, config);
            } catch (IOException ioe) {
                log.error("[HTML] 获取章节内容网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "HTML获取章节内容网络异常: " + ioe.getMessage());
            }
            waitForChapterContentAsyncContent(config);
            return parseChapterContentFromDocument(doc, config);
        } catch (Exception e) {
            log.error("[HTML] 获取章节内容异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "HTML获取章节内容异常: " + e.getMessage());
        }
    }

    private Document fetchChapterContentDocument(String chapterUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(chapterUrl, config);
    }

    private void waitForChapterContentAsyncContent(CrawlerRuleNovel config) {
        waitForAsyncContent(config.getCrawlerRuleContent().getWaitForSelector(), MAX_WAIT_TIME);
    }

    private NovelBook.NovelChapter parseChapterContentFromDocument(Document doc, CrawlerRuleNovel config) {
        // 这里不再调用fetchXXXDocument，不会抛出IOException
        return extractChapterContent(doc, config.getCrawlerRuleContent());
    }

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    @Override
    public String getStrategyName() {
        return "HTML";
    }

    // ================== 私有工具方法 ==================

    /**
     * 构建搜索URL
     *
     * @param searchCfg 搜索配置
     * @param keyword   搜索关键词
     * @return 拼接后的URL
     */
    private String buildSearchUrl(CrawlerRuleSearch searchCfg, String keyword) {
        if (searchCfg == null || searchCfg.getUrl() == null) return "";
        return searchCfg.getUrl().replace("{keyword}", keyword == null ? "" : keyword);
    }

    /**
     * 提取字段内容，支持img@src、a@href等写法
     *
     * @param root     根元素
     * @param selector 选择器或属性表达式
     * @return 提取到的内容
     */
    private String extractField(Element root, String selector) {
        if (root == null || selector == null) return "";
        // 支持如img@src、a@href等写法
        if (selector.contains("@")) {
            String[] arr = selector.split("@", 2);
            String sel = arr[0];
            String attr = arr[1];
            Element el = root.select(sel).first();
            return el != null ? el.attr(attr) : "";
        } else {
            Element el = root.select(selector).first();
            return el != null ? el.text() : "";
        }
    }

    /**
     * 提取书籍列表
     *
     * @param doc          DOM文档
     * @param listSelector 列表选择器
     * @param itemSelector 单项选择器
     * @param crawlerRuleSearch 字段映射配置
     * @return 书籍列表
     */
    private List<NovelBook> extractBookList(Document doc, String listSelector, String itemSelector, CrawlerRuleSearch crawlerRuleSearch) {
        List<NovelBook> books = new ArrayList<>();
        if (doc == null || listSelector == null || crawlerRuleSearch == null) return books;
        Elements bookList = doc.select(listSelector);
        for (Element item : bookList.select(itemSelector != null ? itemSelector : ">")) {
            books.add(extractBookFromElement(item, crawlerRuleSearch));
        }
        return books;
    }

    /**
     * 提取单本书籍
     *
     * @param item    单项元素
     * @param crawlerRuleSearch 字段映射配置
     * @return 书籍实体
     */
    private NovelBook extractBookFromElement(Element item, CrawlerRuleSearch crawlerRuleSearch) {
        NovelBook book = new NovelBook();
        if (crawlerRuleSearch.getNameSelector() != null) book.setName(extractField(item, crawlerRuleSearch.getNameSelector()));
        if (crawlerRuleSearch.getAuthorSelector() != null) book.setAuthor(extractField(item, crawlerRuleSearch.getAuthorSelector()));
        if (crawlerRuleSearch.getCoverSelector() != null) book.setCoverUrl(extractField(item, crawlerRuleSearch.getCoverSelector()));
        if (crawlerRuleSearch.getIntroSelector() != null) book.setIntro(extractField(item, crawlerRuleSearch.getIntroSelector()));
        if (crawlerRuleSearch.getCategorySelector() != null) book.setCategory(extractField(item, crawlerRuleSearch.getCategorySelector()));
        if (crawlerRuleSearch.getWordCountSelector() != null) book.setWordCount(extractField(item, crawlerRuleSearch.getWordCountSelector()));
        if (crawlerRuleSearch.getLatestChapterSelector() != null) book.setLastChapter(extractField(item, crawlerRuleSearch.getLatestChapterSelector()));
        if (crawlerRuleSearch.getBookUrlSelector() != null) {
            Element urlEl = item.select(crawlerRuleSearch.getBookUrlSelector()).first();
            if (urlEl != null) {
                String url = crawlerRuleSearch.getBookUrlAttr() != null ? urlEl.attr(crawlerRuleSearch.getBookUrlAttr()) : urlEl.text();
                book.setBookUrl(url);
            }
        }
        return book;
    }

    /**
     * 提取书籍详情
     *
     * @param doc     DOM文档
     * @param infoCfg 字段映射配置
     * @return 书籍详情
     */
    private NovelBook extractBookInfo(Document doc, CrawlerRuleBookInfo infoCfg) {
        NovelBook book = new NovelBook();
        if (doc == null || infoCfg == null) return book;
        if (infoCfg.getNameSelector() != null) book.setName(extractField(doc, infoCfg.getNameSelector()));
        if (infoCfg.getAuthorSelector() != null) book.setAuthor(extractField(doc, infoCfg.getAuthorSelector()));
        if (infoCfg.getCoverSelector() != null) book.setCoverUrl(extractField(doc, infoCfg.getCoverSelector()));
        if (infoCfg.getIntroSelector() != null) book.setIntro(extractField(doc, infoCfg.getIntroSelector()));
        if (infoCfg.getCategorySelector() != null) book.setCategory(extractField(doc, infoCfg.getCategorySelector()));
        if (infoCfg.getWordCountSelector() != null) book.setWordCount(extractField(doc, infoCfg.getWordCountSelector()));
        if (infoCfg.getLatestChapterSelector() != null) book.setLastChapter(extractField(doc, infoCfg.getLatestChapterSelector()));
        if (infoCfg.getStatusSelector() != null) book.setStatus(extractField(doc, infoCfg.getStatusSelector()));
        if (infoCfg.getUpdateTimeSelector() != null) book.setUpdateTime(extractField(doc, infoCfg.getUpdateTimeSelector()));
        if (infoCfg.getChapterListUrlSelector() != null) {
            Element urlEl = doc.select(infoCfg.getChapterListUrlSelector()).first();
            if (urlEl != null) {
                String url = infoCfg.getChapterListUrlAttr() != null ? urlEl.attr(infoCfg.getChapterListUrlAttr()) : urlEl.text();
                book.setChapterListUrl(url);
            }
        }
        return book;
    }

    /**
     * 提取章节列表
     *
     * @param doc     DOM文档
     * @param listCfg 字段映射配置
     * @return 章节列表
     */
    private List<NovelBook.NovelChapter> extractChapterList(Document doc, CrawlerRuleChapter listCfg) {
        List<NovelBook.NovelChapter> chapters = new ArrayList<>();
        if (doc == null || listCfg == null || listCfg.getChapterItemSelector() == null) return chapters;
        Elements chapterEls = doc.select(listCfg.getChapterItemSelector());
        for (Element el : chapterEls) {
            chapters.add(extractChapterFromElement(el, listCfg));
        }
        return chapters;
    }

    /**
     * 提取单个章节
     *
     * @param el      单项元素
     * @param listCfg 字段映射配置
     * @return 章节实体
     */
    private NovelBook.NovelChapter extractChapterFromElement(Element el, CrawlerRuleChapter listCfg) {
        NovelBook.NovelChapter chapter = new NovelBook.NovelChapter();
        if (listCfg.getTitleSelector() != null) chapter.setTitle(extractField(el, listCfg.getTitleSelector()));
        if (listCfg.getUrlSelector() != null) {
            Element urlEl = el.select(listCfg.getUrlSelector()).first();
            if (urlEl != null) {
                String url = listCfg.getUrlAttr() != null ? urlEl.attr(listCfg.getUrlAttr()) : urlEl.text();
                chapter.setUrl(url);
            }
        }
        return chapter;
    }

    /**
     * 提取章节内容
     *
     * @param doc        DOM文档
     * @param contentCfg 字段映射配置
     * @return 章节内容
     */
    private NovelBook.NovelChapter extractChapterContent(Document doc, CrawlerRuleContent contentCfg) {
        NovelBook.NovelChapter chapter = new NovelBook.NovelChapter();
        if (doc == null || contentCfg == null) return chapter;
        if (contentCfg.getTitleSelector() != null) chapter.setTitle(extractField(doc, contentCfg.getTitleSelector()));
        if (contentCfg.getContentSelector() != null) chapter.setContent(extractField(doc, contentCfg.getContentSelector()));
        return chapter;
    }

    /**
     * 等待异步内容加载（占位实现）
     *
     * @param selector      CSS选择器
     * @param maxWaitMillis 最大等待时间（毫秒）
     */
    private void waitForAsyncContent(String selector, int maxWaitMillis) {
        if (selector == null || selector.trim().isEmpty()) return;
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 获取搜索结果页面DOM
     */
    private Document fetchSearchDocument(String searchUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(searchUrl, config);
    }

    /**
     * 等待异步内容加载（如配置）
     */
    private void waitForSearchAsyncContent(CrawlerRuleNovel config) {
        waitForAsyncContent(config.getCrawlerRuleSearch().getWaitForSelector(), MAX_WAIT_TIME);
    }

    /**
     * 解析书籍列表
     */
    private List<NovelBook> parseBookListFromSearch(Document doc, CrawlerRuleNovel config) {
        return extractBookList(doc, config.getCrawlerRuleSearch().getBookListSelector(), config.getCrawlerRuleSearch().getBookItemSelector(), config.getCrawlerRuleSearch());
    }
} 