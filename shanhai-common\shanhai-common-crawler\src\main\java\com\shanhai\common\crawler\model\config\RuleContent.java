package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import java.io.Serializable;

/**
 * 章节内容页解析配置
 * <p>
 * 用于描述如何从章节内容页提取正文内容。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_content", comment = "章节内容页解析配置表")
@TableName("crawler_rule_content")
public class RuleContent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 章节标题选择器
     */
    @Column(value = "title_selector", comment = "章节标题选择器", length = 200)
    private String titleSelector;

    /**
     * 正文内容选择器
     */
    private String contentSelector;

    /**
     * 下一页选择器
     */
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    private String nextPageAttr;

    /**
     * 需要移除的元素选择器
     */
    private String removeSelector;

    /**
     * 内容过滤正则表达式
     */
    private String filterRegex;

    /**
     * 是否启用分页
     */
    @Builder.Default
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    @Builder.Default
    private Integer maxPages = 10;

    /**
     * 内容分隔符
     */
    @Builder.Default
    private String contentSeparator = "\n";

    /**
     * 是否保留HTML格式
     */
    @Builder.Default
    private Boolean keepHtml = false;

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    private Integer waitTime = 3000;

    /**
     * 编码格式
     */
    @Builder.Default
    private String charset = "UTF-8";

    private String ruleId;
}
