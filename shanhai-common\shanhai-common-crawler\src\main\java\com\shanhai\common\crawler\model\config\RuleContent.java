package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * 章节内容页解析配置
 * <p>
 * 用于描述如何从章节内容页提取正文内容。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("crawler_rule_content")
public class RuleContent extends BaseEntity implements Serializable {
    /**
     * 章节标题选择器
     */
    private String titleSelector;

    /**
     * 正文内容选择器
     */
    private String contentSelector;

    /**
     * 下一页选择器
     */
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    private String nextPageAttr;

    /**
     * 需要移除的元素选择器
     */
    private String removeSelector;

    /**
     * 内容过滤正则表达式
     */
    private String filterRegex;

    /**
     * 是否启用分页
     */
    @Builder.Default
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    @Builder.Default
    private Integer maxPages = 10;

    /**
     * 内容分隔符
     */
    @Builder.Default
    private String contentSeparator = "\n";

    /**
     * 是否保留HTML格式
     */
    @Builder.Default
    private Boolean keepHtml = false;

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    private Integer waitTime = 3000;

    /**
     * 编码格式
     */
    @Builder.Default
    private String charset = "UTF-8";

    private String ruleId;
}
