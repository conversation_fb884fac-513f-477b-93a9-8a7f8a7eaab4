# 应用信息
info:
  name: shanhai-novel
  version: 1.0.1
  description: 山海小说爬虫系统

# 应用基础配置
server:
  #  servlet:
  #    # 应用的访问路径
  #    context-path: /
  port: 8200

# Spring配置
spring:
  # 激活环境配置（Maven动态配置）
  profiles:
    active: @spring.profiles.active@
  # 热部署配置
  devtools:
    restart:
      enabled: true
  # 配置导入，改为 include 方式
  config:
    include: logging, mybatis

# 暂时禁用自动建表，等待实体类清理完成
# MyBatis-Plus-Ext 自动建表配置
# mybatis-plus-ext:
#   auto-table:
#     enable: true
#     mode: update
#     show-sql: true
#     scan-package: com.shanhai.common.crawler.model.config

crawler:
  thread-count: 5
  timeout: 5000
  user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  headers:
    Accept: "text/html,application/xhtml+xml,application/xml"
    Accept-Language: "zh-CN,zh;q=0.9"
  cookies:
    sessionid: "your-session-id"
  proxy-list:
    - "127.0.0.1:8888"
    - "127.0.0.2:8888"
  minDelayMs: 1000
  maxDelayMs: 3000