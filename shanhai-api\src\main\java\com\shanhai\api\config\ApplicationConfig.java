package com.shanhai.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.annotation.PostConstruct;

/**
 * 应用程序配置类
 * 
 * 统一管理应用程序的配置，包括：
 * - 数据库配置
 * - 爬虫配置
 * - 安全配置
 * - 其他业务配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@Import({
    // 导入其他配置类
    // DatabaseConfig.class,
    // SecurityConfig.class
})
public class ApplicationConfig {
    
    @PostConstruct
    public void init() {
        log.info("=== 山海小说爬虫系统配置初始化完成 ===");
        log.info("系统版本: 1.0.1");
        log.info("启动模式: 生产模式");
        log.info("========================================");
    }
}
