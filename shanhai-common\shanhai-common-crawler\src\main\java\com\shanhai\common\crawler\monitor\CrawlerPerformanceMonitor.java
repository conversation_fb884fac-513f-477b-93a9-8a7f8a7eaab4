package com.shanhai.common.crawler.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 爬虫性能监控器
 * <p>
 * 监控爬虫系统的各项性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlerPerformanceMonitor {
    
    // ================== 监控指标 ==================
    
    // 请求统计
    private final LongAdder totalRequests = new LongAdder();
    private final LongAdder successfulRequests = new LongAdder();
    private final LongAdder failedRequests = new LongAdder();
    
    // 响应时间统计
    private final LongAdder totalResponseTime = new LongAdder();
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    
    // 分类统计
    private final Map<String, OperationStats> operationStats = new ConcurrentHashMap<>();
    private final Map<String, SourceStats> sourceStats = new ConcurrentHashMap<>();
    
    // 系统资源统计
    private final AtomicLong peakMemoryUsage = new AtomicLong(0);
    private final AtomicLong peakThreadCount = new AtomicLong(0);
    
    // 监控任务调度器
    private ScheduledExecutorService monitorScheduler;
    
    @PostConstruct
    public void init() {
        // 启动定期监控任务
        monitorScheduler = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "performance-monitor"));
        
        // 每分钟记录一次性能指标
        monitorScheduler.scheduleAtFixedRate(
            this::recordSystemMetrics, 
            1, 1, TimeUnit.MINUTES
        );
        
        // 每10分钟输出一次性能报告
        monitorScheduler.scheduleAtFixedRate(
            this::logPerformanceReport, 
            10, 10, TimeUnit.MINUTES
        );
        
        log.info("性能监控器初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        if (monitorScheduler != null) {
            monitorScheduler.shutdown();
        }
        log.info("性能监控器已关闭");
    }
    
    // ================== 监控记录方法 ==================
    
    /**
     * 记录请求开始
     */
    public RequestContext startRequest(String operation, String source) {
        totalRequests.increment();
        return new RequestContext(operation, source, System.currentTimeMillis());
    }
    
    /**
     * 记录请求成功
     */
    public void recordSuccess(RequestContext context) {
        if (context == null) return;
        
        long duration = System.currentTimeMillis() - context.getStartTime();
        
        successfulRequests.increment();
        totalResponseTime.add(duration);
        
        // 更新最大最小响应时间
        updateResponseTimeRange(duration);
        
        // 更新操作统计
        updateOperationStats(context.getOperation(), duration, true);
        
        // 更新数据源统计
        updateSourceStats(context.getSource(), duration, true);
        
        log.debug("请求成功: {} - {} ({}ms)", context.getOperation(), context.getSource(), duration);
    }
    
    /**
     * 记录请求失败
     */
    public void recordFailure(RequestContext context, Exception error) {
        if (context == null) return;
        
        long duration = System.currentTimeMillis() - context.getStartTime();
        
        failedRequests.increment();
        
        // 更新操作统计
        updateOperationStats(context.getOperation(), duration, false);
        
        // 更新数据源统计
        updateSourceStats(context.getSource(), duration, false);
        
        log.debug("请求失败: {} - {} ({}ms) - {}", 
            context.getOperation(), context.getSource(), duration, error.getMessage());
    }
    
    /**
     * 记录自定义指标
     */
    public void recordCustomMetric(String name, long value) {
        // 可以扩展为支持自定义指标
        log.debug("自定义指标: {} = {}", name, value);
    }
    
    // ================== 私有方法 ==================
    
    private void updateResponseTimeRange(long duration) {
        // 更新最大响应时间
        long currentMax = maxResponseTime.get();
        while (duration > currentMax && !maxResponseTime.compareAndSet(currentMax, duration)) {
            currentMax = maxResponseTime.get();
        }
        
        // 更新最小响应时间
        long currentMin = minResponseTime.get();
        while (duration < currentMin && !minResponseTime.compareAndSet(currentMin, duration)) {
            currentMin = minResponseTime.get();
        }
    }
    
    private void updateOperationStats(String operation, long duration, boolean success) {
        operationStats.computeIfAbsent(operation, k -> new OperationStats())
            .update(duration, success);
    }
    
    private void updateSourceStats(String source, long duration, boolean success) {
        sourceStats.computeIfAbsent(source, k -> new SourceStats())
            .update(duration, success);
    }
    
    private void recordSystemMetrics() {
        Runtime runtime = Runtime.getRuntime();
        
        // 记录内存使用
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long currentPeak = peakMemoryUsage.get();
        while (usedMemory > currentPeak && !peakMemoryUsage.compareAndSet(currentPeak, usedMemory)) {
            currentPeak = peakMemoryUsage.get();
        }
        
        // 记录线程数
        long threadCount = Thread.activeCount();
        long currentThreadPeak = peakThreadCount.get();
        while (threadCount > currentThreadPeak && !peakThreadCount.compareAndSet(currentThreadPeak, threadCount)) {
            currentThreadPeak = peakThreadCount.get();
        }
    }
    
    private void logPerformanceReport() {
        PerformanceReport report = generateReport();
        log.info("性能报告:\n{}", report.getFormattedReport());
    }
    
    // ================== 统计信息获取 ==================
    
    /**
     * 生成性能报告
     */
    public PerformanceReport generateReport() {
        long total = totalRequests.sum();
        long successful = successfulRequests.sum();
        long failed = failedRequests.sum();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0.0;
        double avgResponseTime = successful > 0 ? (double) totalResponseTime.sum() / successful : 0.0;
        
        Runtime runtime = Runtime.getRuntime();
        long currentMemory = runtime.totalMemory() - runtime.freeMemory();
        
        return PerformanceReport.builder()
            .totalRequests(total)
            .successfulRequests(successful)
            .failedRequests(failed)
            .successRate(successRate)
            .averageResponseTime(avgResponseTime)
            .maxResponseTime(maxResponseTime.get())
            .minResponseTime(minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get())
            .currentMemoryUsage(currentMemory)
            .peakMemoryUsage(peakMemoryUsage.get())
            .currentThreadCount(Thread.activeCount())
            .peakThreadCount(peakThreadCount.get())
            .operationStats(Map.copyOf(operationStats))
            .sourceStats(Map.copyOf(sourceStats))
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalRequests.reset();
        successfulRequests.reset();
        failedRequests.reset();
        totalResponseTime.reset();
        maxResponseTime.set(0);
        minResponseTime.set(Long.MAX_VALUE);
        peakMemoryUsage.set(0);
        peakThreadCount.set(0);
        operationStats.clear();
        sourceStats.clear();
        
        log.info("性能统计信息已重置");
    }
    
    // ================== 内部类 ==================
    
    /**
     * 请求上下文
     */
    public static class RequestContext {
        private final String operation;
        private final String source;
        private final long startTime;
        
        public RequestContext(String operation, String source, long startTime) {
            this.operation = operation;
            this.source = source;
            this.startTime = startTime;
        }
        
        public String getOperation() { return operation; }
        public String getSource() { return source; }
        public long getStartTime() { return startTime; }
    }
    
    /**
     * 操作统计
     */
    private static class OperationStats {
        private final LongAdder totalCount = new LongAdder();
        private final LongAdder successCount = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        
        public void update(long duration, boolean success) {
            totalCount.increment();
            totalTime.add(duration);
            if (success) {
                successCount.increment();
            }
        }
        
        public long getTotalCount() { return totalCount.sum(); }
        public long getSuccessCount() { return successCount.sum(); }
        public double getSuccessRate() { 
            long total = getTotalCount();
            return total > 0 ? (double) getSuccessCount() / total * 100 : 0.0; 
        }
        public double getAverageTime() { 
            long total = getTotalCount();
            return total > 0 ? (double) totalTime.sum() / total : 0.0; 
        }
    }
    
    /**
     * 数据源统计
     */
    private static class SourceStats {
        private final LongAdder totalCount = new LongAdder();
        private final LongAdder successCount = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        
        public void update(long duration, boolean success) {
            totalCount.increment();
            totalTime.add(duration);
            if (success) {
                successCount.increment();
            }
        }
        
        public long getTotalCount() { return totalCount.sum(); }
        public long getSuccessCount() { return successCount.sum(); }
        public double getSuccessRate() { 
            long total = getTotalCount();
            return total > 0 ? (double) getSuccessCount() / total * 100 : 0.0; 
        }
        public double getAverageTime() { 
            long total = getTotalCount();
            return total > 0 ? (double) totalTime.sum() / total : 0.0; 
        }
    }
    
    /**
     * 性能报告
     */
    @lombok.Builder
    @lombok.Data
    public static class PerformanceReport {
        private long totalRequests;
        private long successfulRequests;
        private long failedRequests;
        private double successRate;
        private double averageResponseTime;
        private long maxResponseTime;
        private long minResponseTime;
        private long currentMemoryUsage;
        private long peakMemoryUsage;
        private long currentThreadCount;
        private long peakThreadCount;
        private Map<String, OperationStats> operationStats;
        private Map<String, SourceStats> sourceStats;
        private long timestamp;
        
        public String getFormattedReport() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 爬虫性能报告 ===\n");
            sb.append("时间: ").append(new java.util.Date(timestamp)).append("\n");
            sb.append("总请求数: ").append(totalRequests).append("\n");
            sb.append("成功请求: ").append(successfulRequests).append("\n");
            sb.append("失败请求: ").append(failedRequests).append("\n");
            sb.append("成功率: ").append(String.format("%.2f%%", successRate)).append("\n");
            sb.append("平均响应时间: ").append(String.format("%.2fms", averageResponseTime)).append("\n");
            sb.append("最大响应时间: ").append(maxResponseTime).append("ms\n");
            sb.append("最小响应时间: ").append(minResponseTime).append("ms\n");
            sb.append("当前内存使用: ").append(formatBytes(currentMemoryUsage)).append("\n");
            sb.append("峰值内存使用: ").append(formatBytes(peakMemoryUsage)).append("\n");
            sb.append("当前线程数: ").append(currentThreadCount).append("\n");
            sb.append("峰值线程数: ").append(peakThreadCount).append("\n");
            
            if (!operationStats.isEmpty()) {
                sb.append("\n操作统计:\n");
                operationStats.forEach((op, stats) -> 
                    sb.append(String.format("  %s: %d次, 成功率%.2f%%, 平均%.2fms\n", 
                        op, stats.getTotalCount(), stats.getSuccessRate(), stats.getAverageTime())));
            }
            
            if (!sourceStats.isEmpty()) {
                sb.append("\n数据源统计:\n");
                sourceStats.forEach((source, stats) -> 
                    sb.append(String.format("  %s: %d次, 成功率%.2f%%, 平均%.2fms\n", 
                        source, stats.getTotalCount(), stats.getSuccessRate(), stats.getAverageTime())));
            }
            
            sb.append("==================\n");
            return sb.toString();
        }
        
        private String formatBytes(long bytes) {
            if (bytes < 1024) return bytes + " B";
            if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
            if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}
