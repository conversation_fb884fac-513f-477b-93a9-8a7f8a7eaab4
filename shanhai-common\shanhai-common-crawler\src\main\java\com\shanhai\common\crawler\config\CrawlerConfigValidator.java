package com.shanhai.common.crawler.config;

import cn.hutool.core.util.StrUtil;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.config.CrawlerRuleAntiSpider;
import com.shanhai.common.crawler.model.config.CrawlerRuleBookInfo;
import com.shanhai.common.crawler.model.config.CrawlerRuleChapter;
import com.shanhai.common.crawler.model.config.CrawlerRuleContent;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.model.config.CrawlerRuleSearch;
import com.shanhai.common.crawler.utils.CrawlerToolkit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 爬虫配置验证器
 * <p>
 * 提供详细的配置验证和错误报告功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlerConfigValidator {
    
    // URL正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile("https?://[^\\s/$.?#].[^\\s]*");
    
    // CSS选择器正则表达式
    private static final Pattern CSS_SELECTOR_PATTERN = Pattern.compile("[.#]?[\\w-]+(?:[.#][\\w-]+)*(?:\\[[^\\]]+\\])?(?::[\\w-]+(?:\\([^)]+\\))?)*");
    
    /**
     * 验证爬虫规则配置
     */
    public void validateRule(CrawlerRuleNovel rule) {
        List<String> errors = new ArrayList<>();
        
        // 验证基本信息
        if (rule == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "爬虫规则不能为空");
        }
        
        if (StrUtil.isBlank(rule.getSourceName())) {
            errors.add("站点名称不能为空");
        }
        
        if (StrUtil.isBlank(rule.getSourceUrl())) {
            errors.add("站点URL不能为空");
        } else if (!CrawlerToolkit.isValidUrl(rule.getSourceUrl())) {
            errors.add("站点URL格式无效: " + rule.getSourceUrl());
        }
        
        if (StrUtil.isBlank(rule.getMode())) {
            errors.add("采集模式不能为空");
        }
        
        // 验证搜索规则
        validateSearchRule(rule.getCrawlerRuleSearch(), errors);
        
        // 验证书籍详情规则
        validateBookInfoRule(rule.getCrawlerRuleBookInfo(), errors);
        
        // 验证章节列表规则
        validateChapterRule(rule.getCrawlerRuleChapter(), errors);
        
        // 验证章节内容规则
        validateContentRule(rule.getCrawlerRuleContent(), errors);
        
        // 验证反爬虫规则
        validateAntiSpiderRule(rule.getCrawlerRuleAntiSpider(), errors);
        
        // 如果有错误，抛出异常
        if (!errors.isEmpty()) {
            String errorMessage = String.format("规则验证失败 (%s): %s", 
                rule.getSourceName(), String.join("; ", errors));
            log.warn(errorMessage);
            throw new CrawlerException(CrawlerErrorCode.RULE_VALIDATION_FAILED, errorMessage);
        }
        
        log.debug("规则验证通过: {}", rule.getSourceName());
    }
    
    /**
     * 验证搜索规则
     */
    private void validateSearchRule(CrawlerRuleSearch rule, List<String> errors) {
        if (rule == null) {
            errors.add("搜索规则不能为空");
            return;
        }
        
        if (StrUtil.isBlank(rule.getUrl())) {
            errors.add("搜索URL不能为空");
        } else if (!rule.getUrl().contains("{keyword}") && 
                   !rule.getUrl().contains("{KEYWORD}") && 
                   !rule.getUrl().contains("${keyword}")) {
            errors.add("搜索URL必须包含关键词占位符: " + rule.getUrl());
        }
        
        if (StrUtil.isBlank(rule.getNameSelector())) {
            errors.add("书名选择器不能为空");
        } else if (!isValidSelector(rule.getNameSelector())) {
            errors.add("书名选择器格式无效: " + rule.getNameSelector());
        }
        
        if (StrUtil.isBlank(rule.getAuthorSelector())) {
            errors.add("作者选择器不能为空");
        } else if (!isValidSelector(rule.getAuthorSelector())) {
            errors.add("作者选择器格式无效: " + rule.getAuthorSelector());
        }
        
        if (StrUtil.isBlank(rule.getDetailUrlSelector())) {
            errors.add("详情页URL选择器不能为空");
        } else if (!isValidSelector(rule.getDetailUrlSelector())) {
            errors.add("详情页URL选择器格式无效: " + rule.getDetailUrlSelector());
        }
    }
    
    /**
     * 验证书籍详情规则
     */
    private void validateBookInfoRule(CrawlerRuleBookInfo rule, List<String> errors) {
        if (rule == null) {
            errors.add("书籍详情规则不能为空");
            return;
        }
        
        if (StrUtil.isBlank(rule.getNameSelector())) {
            errors.add("书名选择器不能为空");
        } else if (!isValidSelector(rule.getNameSelector())) {
            errors.add("书名选择器格式无效: " + rule.getNameSelector());
        }
        
        if (StrUtil.isBlank(rule.getAuthorSelector())) {
            errors.add("作者选择器不能为空");
        } else if (!isValidSelector(rule.getAuthorSelector())) {
            errors.add("作者选择器格式无效: " + rule.getAuthorSelector());
        }
        
        if (StrUtil.isBlank(rule.getChapterListUrlSelector()) && StrUtil.isBlank(rule.getChapterListUrl())) {
            errors.add("章节列表URL选择器和固定URL不能同时为空");
        }
        
        if (StrUtil.isNotBlank(rule.getChapterListUrlSelector()) && !isValidSelector(rule.getChapterListUrlSelector())) {
            errors.add("章节列表URL选择器格式无效: " + rule.getChapterListUrlSelector());
        }
        
        if (StrUtil.isNotBlank(rule.getChapterListUrl()) && !CrawlerToolkit.isValidUrl(rule.getChapterListUrl())) {
            errors.add("章节列表固定URL格式无效: " + rule.getChapterListUrl());
        }
    }
    
    /**
     * 验证章节列表规则
     */
    private void validateChapterRule(CrawlerRuleChapter rule, List<String> errors) {
        if (rule == null) {
            errors.add("章节列表规则不能为空");
            return;
        }
        
        if (StrUtil.isBlank(rule.getListSelector())) {
            errors.add("章节列表选择器不能为空");
        } else if (!isValidSelector(rule.getListSelector())) {
            errors.add("章节列表选择器格式无效: " + rule.getListSelector());
        }
        
        if (StrUtil.isBlank(rule.getTitleSelector())) {
            errors.add("章节标题选择器不能为空");
        } else if (!isValidSelector(rule.getTitleSelector())) {
            errors.add("章节标题选择器格式无效: " + rule.getTitleSelector());
        }
        
        if (StrUtil.isBlank(rule.getUrlSelector())) {
            errors.add("章节URL选择器不能为空");
        } else if (!isValidSelector(rule.getUrlSelector())) {
            errors.add("章节URL选择器格式无效: " + rule.getUrlSelector());
        }
    }
    
    /**
     * 验证章节内容规则
     */
    private void validateContentRule(CrawlerRuleContent rule, List<String> errors) {
        if (rule == null) {
            errors.add("章节内容规则不能为空");
            return;
        }
        
        if (StrUtil.isBlank(rule.getContentSelector())) {
            errors.add("章节内容选择器不能为空");
        } else if (!isValidSelector(rule.getContentSelector())) {
            errors.add("章节内容选择器格式无效: " + rule.getContentSelector());
        }
        
        if (StrUtil.isNotBlank(rule.getTitleSelector()) && !isValidSelector(rule.getTitleSelector())) {
            errors.add("章节标题选择器格式无效: " + rule.getTitleSelector());
        }
    }
    
    /**
     * 验证反爬虫规则
     */
    private void validateAntiSpiderRule(CrawlerRuleAntiSpider rule, List<String> errors) {
        if (rule == null) {
            return; // 反爬虫规则可选
        }
        
        if (rule.getTimeout() != null && rule.getTimeout() < 0) {
            errors.add("超时时间不能为负数: " + rule.getTimeout());
        }
        
        if (rule.getMinDelayMs() != null && rule.getMaxDelayMs() != null) {
            if (rule.getMinDelayMs() < 0) {
                errors.add("最小延迟不能为负数: " + rule.getMinDelayMs());
            }
            
            if (rule.getMaxDelayMs() < rule.getMinDelayMs()) {
                errors.add("最大延迟不能小于最小延迟: " + rule.getMaxDelayMs() + " < " + rule.getMinDelayMs());
            }
        }
        
        if (rule.getMaxRetries() != null && rule.getMaxRetries() < 0) {
            errors.add("最大重试次数不能为负数: " + rule.getMaxRetries());
        }
    }
    
    /**
     * 验证选择器格式
     */
    private boolean isValidSelector(String selector) {
        if (StrUtil.isBlank(selector)) {
            return false;
        }
        
        // 简单验证，实际上CSS选择器语法很复杂
        // 这里只做基本检查，避免明显错误
        return !selector.contains("<") && !selector.contains(">") && 
               !selector.contains("\"") && !selector.contains("'") && 
               selector.length() < 500;
    }
    
    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        
        return URL_PATTERN.matcher(url).matches();
    }
    
    /**
     * 验证规则是否有效
     */
    public boolean isRuleValid(CrawlerRuleNovel rule) {
        try {
            validateRule(rule);
            return true;
        } catch (Exception e) {
            log.debug("规则验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取规则验证结果
     */
    public ValidationResult validateRuleWithResult(CrawlerRuleNovel rule) {
        List<String> errors = new ArrayList<>();
        
        try {
            validateRule(rule);
            return ValidationResult.success();
        } catch (CrawlerException e) {
            return ValidationResult.failure(e.getMessage());
        } catch (Exception e) {
            return ValidationResult.failure("验证过程异常: " + e.getMessage());
        }
    }
    
    /**
     * 验证结果类
     */
    @lombok.Data
    @lombok.Builder
    public static class ValidationResult {
        private boolean valid;
        private String message;
        private List<String> errors;
        
        public static ValidationResult success() {
            return ValidationResult.builder()
                .valid(true)
                .message("验证通过")
                .errors(new ArrayList<>())
                .build();
        }
        
        public static ValidationResult failure(String message) {
            return ValidationResult.builder()
                .valid(false)
                .message(message)
                .errors(List.of(message))
                .build();
        }
        
        public static ValidationResult failure(List<String> errors) {
            return ValidationResult.builder()
                .valid(false)
                .message(String.join("; ", errors))
                .errors(errors)
                .build();
        }
    }
}
