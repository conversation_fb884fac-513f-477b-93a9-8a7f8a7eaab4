package com.shanhai.api.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * MyBatis 配置类
 * 
 * 专门用于解决 ACTable 与 MyBatis-Plus 的兼容性问题
 * 
 * <AUTHOR>
 */
@Configuration
public class MyBatisConfig {

    @Autowired
    private DataSource dataSource;

    @Bean
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置实体类别名包路径
        sessionFactory.setTypeAliasesPackage("com.shanhai.**.model");
        
        // 加载所有的 Mapper XML 文件，包括 ACTable 的
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        List<Resource> resources = new ArrayList<>();
        
        try {
            // 加载项目的 mapper 文件
            resources.addAll(Arrays.asList(resolver.getResources("classpath*:mapper/**/*.xml")));
            
            // 加载 ACTable 的 mapper 文件
            resources.addAll(Arrays.asList(resolver.getResources("classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/**/*.xml")));
            
        } catch (IOException e) {
            // 如果 ACTable 的 mapper 文件路径不存在，忽略错误
            System.out.println("Warning: ACTable mapper files not found, this is normal if ACTable is not properly configured");
        }
        
        sessionFactory.setMapperLocations(resources.toArray(new Resource[0]));
        
        return sessionFactory.getObject();
    }
}
