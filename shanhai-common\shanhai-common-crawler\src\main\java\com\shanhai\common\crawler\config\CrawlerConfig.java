package com.shanhai.common.crawler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 爬虫配置类
 * <p>
 * 统一管理爬虫相关的配置参数
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "crawler")
public class CrawlerConfig {

    /**
     * 线程池大小
     */
    private int threadCount = 5;

    /**
     * 默认超时时间（毫秒）
     */
    private int timeout = 10000;

    /**
     * 默认User-Agent
     */
    private String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";

    /**
     * 默认请求头
     */
    private Map<String, String> headers;

    /**
     * 默认Cookie
     */
    private Map<String, String> cookies;

    /**
     * 代理列表
     */
    private List<String> proxyList;

    /**
     * 最小延迟时间（毫秒）
     */
    private int minDelayMs = 1000;

    /**
     * 最大延迟时间（毫秒）
     */
    private int maxDelayMs = 3000;

    /**
     * 重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（分钟）
     */
    private int cacheExpireMinutes = 5;

    /**
     * 是否启用反爬虫检测
     */
    private boolean antiSpiderEnabled = true;

    /**
     * 批量处理大小
     */
    private int batchSize = 10;

    /**
     * 是否启用调试模式
     */
    private boolean debugMode = false;

    /**
     * 日志级别
     */
    private String logLevel = "INFO";

    /**
     * 允许的域名列表（白名单）
     */
    private List<String> allowedDomains;

    /**
     * 禁止的域名列表（黑名单）
     */
    private List<String> blockedDomains;

    /**
     * 数据库连接池配置
     */
    private DatabaseConfig database = new DatabaseConfig();

    /**
     * 数据库配置内部类
     */
    @Data
    public static class DatabaseConfig {
        /**
         * 最大连接数
         */
        private int maxConnections = 20;

        /**
         * 最小连接数
         */
        private int minConnections = 5;

        /**
         * 连接超时时间（秒）
         */
        private int connectionTimeout = 30;

        /**
         * 查询超时时间（秒）
         */
        private int queryTimeout = 60;
    }

    /**
     * 获取随机延迟时间
     */
    public long getRandomDelay() {
        if (minDelayMs >= maxDelayMs) {
            return minDelayMs;
        }
        return minDelayMs + (long) (Math.random() * (maxDelayMs - minDelayMs));
    }

    /**
     * 检查域名是否被允许
     */
    public boolean isDomainAllowed(String domain) {
        if (allowedDomains == null || allowedDomains.isEmpty()) {
            return true; // 如果没有白名单，默认允许
        }
        return allowedDomains.contains(domain);
    }

    /**
     * 检查域名是否被禁止
     */
    public boolean isDomainBlocked(String domain) {
        if (blockedDomains == null || blockedDomains.isEmpty()) {
            return false; // 如果没有黑名单，默认不禁止
        }
        return blockedDomains.contains(domain);
    }

    /**
     * 验证配置参数
     */
    public void validate() {
        if (threadCount <= 0) {
            throw new IllegalArgumentException("线程池大小必须大于0");
        }
        if (timeout <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }
        if (minDelayMs < 0) {
            throw new IllegalArgumentException("最小延迟时间不能小于0");
        }
        if (maxDelayMs < minDelayMs) {
            throw new IllegalArgumentException("最大延迟时间不能小于最小延迟时间");
        }
        if (maxRetryCount < 0) {
            throw new IllegalArgumentException("重试次数不能小于0");
        }
        if (batchSize <= 0) {
            throw new IllegalArgumentException("批量处理大小必须大于0");
        }
        if (cacheExpireMinutes <= 0) {
            throw new IllegalArgumentException("缓存过期时间必须大于0");
        }
    }
}
