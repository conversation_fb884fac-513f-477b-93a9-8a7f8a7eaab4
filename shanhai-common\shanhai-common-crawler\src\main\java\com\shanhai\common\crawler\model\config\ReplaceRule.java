package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * 内容替换规则
 * <p>
 * 用于对采集到的内容进行正则替换处理。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("crawler_replace_rule")
public class ReplaceRule extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /** 匹配正则表达式 */
    private String pattern;

    /** 替换内容 */
    private String replacement;

    /** 规则名称 */
    private String ruleName;

    /** 规则描述 */
    private String ruleDescription;

    /** 是否启用 */
    @Builder.Default
    private Boolean enabled = true;

    /** 排序 */
    @Builder.Default
    private Integer sortOrder = 0;

    private String ruleId;
} 