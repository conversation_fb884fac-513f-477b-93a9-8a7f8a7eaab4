package com.shanhai.common.crawler.strategy;

import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import com.shanhai.common.crawler.model.config.CrawlerRuleChapter;
import com.shanhai.common.crawler.model.config.CrawlerRuleSearch;
import com.shanhai.common.crawler.utils.CrawlerNetworkManager;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * API采集策略实现
 * <p>
 * 通过API接口采集书籍、详情、章节列表和章节内容，自动解析JSON并通过字段映射封装实体对象。
 * 线程安全：无状态实现，线程安全。
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class ApiCrawlerStrategy implements CrawlerStrategy {
    /**
     * 搜索书籍（API模式）
     *
     * @param config  采集规则配置
     * @param keyword 搜索关键词
     * @return 书籍列表
     * @throws CrawlerException 采集异常
     */
    @Override
    public List<NovelBook> searchBooks(CrawlerRuleNovel config, String keyword) throws CrawlerException {
        if (config == null || config.getCrawlerRuleSearch() == null) return new ArrayList<>();
        try {
            String searchUrl = buildSearchUrl(config, keyword);
            org.jsoup.nodes.Document doc;
            try {
                doc = fetchSearchDocument(searchUrl, config);
            } catch (IOException ioe) {
                log.error("[API] 搜索书籍网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "API搜索书籍网络异常: " + ioe.getMessage());
            }
            String json = extractJsonFromDocument(doc);
            return parseBookListFromJson(json, config);
        } catch (Exception e) {
            log.error("[API] 搜索书籍异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "API搜索书籍异常: " + e.getMessage());
        }
    }

    private org.jsoup.nodes.Document fetchSearchDocument(String searchUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(searchUrl, config);
    }

    private String extractJsonFromDocument(org.jsoup.nodes.Document doc) {
        return doc.body().text();
    }

    /**
     * 获取书籍详情（API模式）
     *
     * @param config  采集规则配置
     * @param bookUrl 详情页URL
     * @return 书籍详情
     * @throws CrawlerException 采集异常
     */
    @Override
    public NovelBook getBookInfo(CrawlerRuleNovel config, String bookUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleBookInfo() == null) return new NovelBook();
        try {
            org.jsoup.nodes.Document doc;
            try {
                doc = fetchBookInfoDocument(bookUrl, config);
            } catch (IOException ioe) {
                log.error("[API] 获取书籍详情网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "API获取书籍详情网络异常: " + ioe.getMessage());
            }
            String json = extractJsonFromDocument(doc);
            return parseBookInfoFromJson(json, config);
        } catch (Exception e) {
            log.error("[API] 获取书籍详情异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "API获取书籍详情异常: " + e.getMessage());
        }
    }

    private org.jsoup.nodes.Document fetchBookInfoDocument(String bookUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(bookUrl, config);
    }

    /**
     * 获取章节列表（API模式）
     *
     * @param config         采集规则配置
     * @param chapterListUrl 章节列表页URL
     * @return 章节列表
     * @throws CrawlerException 采集异常
     */
    @Override
    public List<NovelBook.NovelChapter> getChapterList(CrawlerRuleNovel config, String chapterListUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleChapter() == null) return new ArrayList<>();
        try {
            org.jsoup.nodes.Document doc;
            try {
                doc = fetchChapterListDocument(chapterListUrl, config);
            } catch (IOException ioe) {
                log.error("[API] 获取章节列表网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "API获取章节列表网络异常: " + ioe.getMessage());
            }
            String json = extractJsonFromDocument(doc);
            return parseChapterListFromJson(json, config);
        } catch (Exception e) {
            log.error("[API] 获取章节列表异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "API获取章节列表异常: " + e.getMessage());
        }
    }

    private org.jsoup.nodes.Document fetchChapterListDocument(String chapterListUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(chapterListUrl, config);
    }

    /**
     * 获取章节内容（API模式）
     *
     * @param config     采集规则配置
     * @param chapterUrl 章节内容页URL
     * @return 章节内容
     * @throws CrawlerException 采集异常
     */
    @Override
    public NovelBook.NovelChapter getChapterContent(CrawlerRuleNovel config, String chapterUrl) throws CrawlerException {
        if (config == null || config.getCrawlerRuleContent() == null) return new NovelBook.NovelChapter();
        try {
            org.jsoup.nodes.Document doc;
            try {
                doc = fetchChapterContentDocument(chapterUrl, config);
            } catch (IOException ioe) {
                log.error("[API] 获取章节内容网络异常: {}", ioe.getMessage(), ioe);
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "API获取章节内容网络异常: " + ioe.getMessage());
            }
            String json = extractJsonFromDocument(doc);
            return parseChapterContentFromJson(json, config);
        } catch (Exception e) {
            log.error("[API] 获取章节内容异常: {}", e.getMessage(), e);
            throw new CrawlerException(CrawlerErrorCode.PARSE_ERROR, "API获取章节内容异常: " + e.getMessage());
        }
    }

    private org.jsoup.nodes.Document fetchChapterContentDocument(String chapterUrl, CrawlerRuleNovel config) throws IOException {
        return CrawlerNetworkManager.getDocument(chapterUrl, config);
    }

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    @Override
    public String getStrategyName() {
        return "API";
    }

    // ================== 私有工具方法 ==================

    /**
     * 构建搜索URL
     *
     * @param config  采集规则配置
     * @param keyword 搜索关键词
     * @return 拼接后的URL
     */
    private String buildSearchUrl(CrawlerRuleNovel config, String keyword) {
        if (config == null || config.getCrawlerRuleSearch() == null || config.getCrawlerRuleSearch().getUrl() == null) return "";
        return config.getCrawlerRuleSearch().getUrl().replace("{keyword}", keyword == null ? "" : keyword);
    }

    /**
     * 解析书籍列表JSON
     *
     * @param json   API返回的JSON字符串
     * @param config 采集规则配置
     * @return 书籍列表
     */
    private List<NovelBook> parseBookListFromJson(String json, CrawlerRuleNovel config) {
        List<NovelBook> books = new ArrayList<>();
        if (json == null || json.trim().isEmpty() || config == null || config.getCrawlerRuleSearch() == null) return books;
        // 这里只做JSONArray示例，实际可根据API结构扩展
        try {
            cn.hutool.json.JSONArray arr = cn.hutool.json.JSONUtil.parseArray(json);
            for (Object obj : arr) {
                books.add(mapJsonToBook(obj, config.getCrawlerRuleSearch()));
            }
        } catch (Exception e) {
            log.warn("[API] 解析书籍列表JSON异常: {}", e.getMessage());
        }
        return books;
    }

    /**
     * 解析书籍详情JSON
     *
     * @param json   API返回的JSON字符串
     * @param config 采集规则配置
     * @return 书籍详情
     */
    private NovelBook parseBookInfoFromJson(String json, CrawlerRuleNovel config) {
        if (json == null || json.trim().isEmpty() || config == null || config.getCrawlerRuleBookInfo() == null) return new NovelBook();
        try {
            cn.hutool.json.JSONObject obj = cn.hutool.json.JSONUtil.parseObj(json);
            return mapJsonToBook(obj, config.getCrawlerRuleSearch() != null ? config.getCrawlerRuleSearch() : null);
        } catch (Exception e) {
            log.warn("[API] 解析书籍详情JSON异常: {}", e.getMessage());
            return new NovelBook();
        }
    }

    /**
     * 解析章节列表JSON
     *
     * @param json   API返回的JSON字符串
     * @param config 采集规则配置
     * @return 章节列表
     */
    private List<NovelBook.NovelChapter> parseChapterListFromJson(String json, CrawlerRuleNovel config) {
        List<NovelBook.NovelChapter> chapters = new ArrayList<>();
        if (json == null || json.trim().isEmpty() || config == null || config.getCrawlerRuleChapter() == null) return chapters;
        // 这里只做JSONArray示例，实际可根据API结构扩展
        try {
            cn.hutool.json.JSONArray arr = cn.hutool.json.JSONUtil.parseArray(json);
            for (Object obj : arr) {
                chapters.add(mapJsonToChapter(obj, config.getCrawlerRuleChapter()));
            }
        } catch (Exception e) {
            log.warn("[API] 解析章节列表JSON异常: {}", e.getMessage());
        }
        return chapters;
    }

    /**
     * 解析章节内容JSON
     *
     * @param json   API返回的JSON字符串
     * @param config 采集规则配置
     * @return 章节内容
     */
    private NovelBook.NovelChapter parseChapterContentFromJson(String json, CrawlerRuleNovel config) {
        if (json == null || json.trim().isEmpty() || config == null || config.getCrawlerRuleContent() == null) return new NovelBook.NovelChapter();
        try {
            cn.hutool.json.JSONObject obj = cn.hutool.json.JSONUtil.parseObj(json);
            return mapJsonToChapter(obj, config.getCrawlerRuleChapter());
        } catch (Exception e) {
            log.warn("[API] 解析章节内容JSON异常: {}", e.getMessage());
            return new NovelBook.NovelChapter();
        }
    }

    /**
     * JSON对象映射为书籍
     *
     * @param obj     JSON对象
     * @param crawlerRuleSearch 字段映射配置
     * @return 书籍实体
     */
    private NovelBook mapJsonToBook(Object obj, CrawlerRuleSearch crawlerRuleSearch) {
        NovelBook book = new NovelBook();
        if (obj == null || crawlerRuleSearch == null) return book;
        cn.hutool.json.JSONObject json = cn.hutool.json.JSONUtil.parseObj(obj);
        // 字段自动映射
        if (crawlerRuleSearch.getNameSelector() != null) book.setName(json.getStr(crawlerRuleSearch.getNameSelector()));
        if (crawlerRuleSearch.getAuthorSelector() != null) book.setAuthor(json.getStr(crawlerRuleSearch.getAuthorSelector()));
        if (crawlerRuleSearch.getCoverSelector() != null) book.setCoverUrl(json.getStr(crawlerRuleSearch.getCoverSelector()));
        if (crawlerRuleSearch.getIntroSelector() != null) book.setIntro(json.getStr(crawlerRuleSearch.getIntroSelector()));
        if (crawlerRuleSearch.getCategorySelector() != null) book.setCategory(json.getStr(crawlerRuleSearch.getCategorySelector()));
        if (crawlerRuleSearch.getWordCountSelector() != null) book.setWordCount(json.getStr(crawlerRuleSearch.getWordCountSelector()));
        if (crawlerRuleSearch.getLastChapterSelector() != null) book.setLastChapter(json.getStr(crawlerRuleSearch.getLastChapterSelector()));
        if (crawlerRuleSearch.getBookUrlSelector() != null) book.setBookUrl(json.getStr(crawlerRuleSearch.getBookUrlSelector()));
        return book;
    }

    /**
     * JSON对象映射为章节
     *
     * @param obj     JSON对象
     * @param listCfg 字段映射配置
     * @return 章节实体
     */
    private NovelBook.NovelChapter mapJsonToChapter(Object obj, CrawlerRuleChapter listCfg) {
        NovelBook.NovelChapter chapter = new NovelBook.NovelChapter();
        if (obj == null || listCfg == null) return chapter;
        cn.hutool.json.JSONObject json = cn.hutool.json.JSONUtil.parseObj(obj);
        if (listCfg.getChapterNameSelector() != null) chapter.setTitle(json.getStr(listCfg.getChapterNameSelector()));
        if (listCfg.getChapterUrlSelector() != null) chapter.setUrl(json.getStr(listCfg.getChapterUrlSelector()));
        return chapter;
    }
} 