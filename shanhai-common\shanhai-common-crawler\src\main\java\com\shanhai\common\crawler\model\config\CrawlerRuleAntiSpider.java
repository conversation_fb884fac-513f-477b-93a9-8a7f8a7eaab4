package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import java.io.Serializable;

/**
 * 反爬虫配置
 * <p>
 * 用于配置反爬虫策略，如延时、代理、User-Agent等。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_anti_spider", comment = "反爬虫配置表")
@TableName("crawler_rule_anti_spider")
public class CrawlerRuleAntiSpider implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 最小延时（毫秒）
     */
    @Builder.Default
    @Column(value = "min_delay_ms", comment = "最小延时(毫秒)", defaultValue = "1000")
    private Integer minDelayMs = 1000;

    /**
     * 最大延时（毫秒）
     */
    @Builder.Default
    private Integer maxDelayMs = 3000;

    /**
     * User-Agent列表（JSON格式）
     */
    private String userAgentList;

    /**
     * 代理服务器列表（JSON格式）
     */
    private String proxyList;

    /**
     * 是否启用代理
     */
    @Builder.Default
    private Boolean enableProxy = false;

    /**
     * 是否随机User-Agent
     */
    @Builder.Default
    private Boolean randomUserAgent = true;

    /**
     * 最大重试次数
     */
    @Builder.Default
    private Integer maxRetries = 3;

    /**
     * 超时时间（毫秒）
     */
    @Builder.Default
    private Integer timeout = 30000;

    /**
     * 是否启用Cookie
     */
    @Builder.Default
    private Boolean enableCookie = true;

    /**
     * 自定义请求头（JSON格式）
     */
    private String customHeaders;

    /**
     * 验证码处理策略
     */
    private String captchaStrategy;

    /**
     * 是否启用JavaScript渲染
     */
    @Builder.Default
    private Boolean enableJavaScript = false;

    private String ruleId;
}
