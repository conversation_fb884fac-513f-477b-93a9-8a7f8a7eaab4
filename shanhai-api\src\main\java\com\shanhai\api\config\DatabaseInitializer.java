package com.shanhai.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库初始化器
 * 
 * 在应用启动时检查并创建必要的数据库表
 * 替代 ACTable 的自动建表功能，避免版本兼容性问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始检查数据库表结构...");
        
        try (Connection connection = dataSource.getConnection()) {
            // 检查表是否存在
            if (!tableExists(connection, "crawler_rule")) {
                log.info("数据库表不存在，开始创建表结构...");
                createTables(connection);
                log.info("数据库表创建完成");
            } else {
                log.info("数据库表已存在，跳过创建");
            }
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            // 不抛出异常，允许应用继续启动
            log.warn("应用将在没有数据库表的情况下启动，某些功能可能不可用");
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(Connection connection, String tableName) throws Exception {
        DatabaseMetaData metaData = connection.getMetaData();
        try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
            return rs.next();
        }
    }

    /**
     * 创建数据库表
     */
    private void createTables(Connection connection) throws Exception {
        // 读取 SQL 脚本
        ClassPathResource resource = new ClassPathResource("sql/init_tables.sql");
        String sql = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        
        // 分割 SQL 语句（按分号分割）
        String[] statements = sql.split(";");
        
        try (Statement statement = connection.createStatement()) {
            for (String sqlStatement : statements) {
                String trimmedSql = sqlStatement.trim();
                if (!trimmedSql.isEmpty() && !trimmedSql.startsWith("--") && !trimmedSql.startsWith("#")) {
                    try {
                        statement.execute(trimmedSql);
                        log.debug("执行SQL: {}", trimmedSql.substring(0, Math.min(50, trimmedSql.length())) + "...");
                    } catch (Exception e) {
                        log.warn("SQL执行失败: {}, 错误: {}", trimmedSql.substring(0, Math.min(50, trimmedSql.length())), e.getMessage());
                    }
                }
            }
        }
    }
}
