package com.shanhai.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库初始化器
 * 
 * 在应用启动时检查并创建必要的数据库表
 * 替代 ACTable 的自动建表功能，避免版本兼容性问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始检查数据库表结构...");
        
        try (Connection connection = dataSource.getConnection()) {
            // 检查表是否存在
            if (!tableExists(connection, "crawler_rule")) {
                log.info("数据库表不存在，开始创建表结构...");
                createTables(connection);
                log.info("数据库表创建完成");
            } else {
                log.info("数据库表已存在，跳过创建");
            }
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            // 不抛出异常，允许应用继续启动
            log.warn("应用将在没有数据库表的情况下启动，某些功能可能不可用");
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(Connection connection, String tableName) throws Exception {
        DatabaseMetaData metaData = connection.getMetaData();
        try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
            return rs.next();
        }
    }

    /**
     * 创建数据库表
     */
    private void createTables(Connection connection) throws Exception {
        // 读取 SQL 脚本
        ClassPathResource resource = new ClassPathResource("sql/init_tables.sql");
        String sql = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);

        log.info("开始执行数据库初始化脚本，脚本长度: {} 字符", sql.length());

        // 使用更简单的方法分割SQL语句
        // 先移除注释行，然后按分号分割
        String cleanedSql = removeComments(sql);
        String[] statements = cleanedSql.split(";");

        log.info("分割后得到 {} 条SQL语句", statements.length);

        int executedCount = 0;
        int successCount = 0;

        try (Statement statement = connection.createStatement()) {
            for (int i = 0; i < statements.length; i++) {
                String sqlStatement = statements[i].trim();
                if (!sqlStatement.isEmpty() &&
                    !sqlStatement.equalsIgnoreCase("COMMIT") &&
                    !sqlStatement.startsWith("USE ")) {

                    executedCount++;
                    log.info("执行第 {} 条SQL语句...", executedCount);

                    try {
                        statement.execute(sqlStatement);
                        successCount++;

                        // 提取表名用于日志
                        String tableName = extractTableName(sqlStatement);
                        if (tableName != null) {
                            log.info("✅ 成功创建表: {}", tableName);
                        } else {
                            log.info("✅ 执行SQL成功: {}", sqlStatement.substring(0, Math.min(50, sqlStatement.length())) + "...");
                        }
                    } catch (Exception e) {
                        log.error("❌ SQL执行失败: {}", sqlStatement.substring(0, Math.min(100, sqlStatement.length())) + "...");
                        log.error("错误详情: {}", e.getMessage());
                        // 继续执行其他语句，不中断整个过程
                    }
                }
            }
        }

        log.info("数据库初始化完成，执行了 {}/{} 条SQL语句", successCount, executedCount);
    }

    /**
     * 移除SQL脚本中的注释
     */
    private String removeComments(String sql) {
        StringBuilder result = new StringBuilder();
        String[] lines = sql.split("\n");

        for (String line : lines) {
            String trimmedLine = line.trim();
            // 跳过注释行
            if (!trimmedLine.startsWith("--") && !trimmedLine.startsWith("#")) {
                result.append(line).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 从SQL语句中提取表名
     */
    private String extractTableName(String sql) {
        String upperSql = sql.toUpperCase().trim();
        if (upperSql.startsWith("CREATE TABLE")) {
            // 匹配 CREATE TABLE IF NOT EXISTS `table_name` 或 CREATE TABLE `table_name`
            String pattern = "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?[`']?([a-zA-Z_][a-zA-Z0-9_]*)[`']?";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(sql);
            if (m.find()) {
                return m.group(1);
            }
        }
        return null;
    }
}
