# ACTable 到 MyBatis-Plus-Ext 迁移指南

## 🎯 迁移目标

从 ACTable 迁移到 MyBatis-Plus-Ext，实现自动建表功能。

## ✅ 已完成的工作

1. **依赖配置**：已添加 `mybatis-plus-ext-spring-boot-starter`
2. **配置文件**：已配置 MyBatis-Plus-Ext 自动建表参数
3. **移除 ACTable**：已移除 ACTable 相关配置和依赖

## 🔧 需要完成的工作

### 1. 清理实体类注解

需要处理以下实体类，移除所有 ACTable 注解：

- `ReplaceRule.java` ❌
- `RuleBookInfo.java` ❌  
- `RuleChapter.java` ❌
- `RuleContent.java` ❌
- `RuleAntiSpider.java` ❌
- `NovelCrawlerRule.java` ❌
- `RuleSearch.java` ✅ (已完成)

### 2. 注解迁移规则

**移除的注解：**
```java
// 移除这些 ACTable 注解
@Table(name = "table_name")
@Column(name = "field_name", comment = "注释", type = MySqlTypeConstant.VARCHAR, length = 100, isNull = false)

// 移除这些导入
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
```

**保留的注解：**
```java
// 保留这些 MyBatis-Plus 注解
@TableName("table_name")
@TableId(value = "id", type = IdType.ASSIGN_UUID)
@TableField(fill = FieldFill.INSERT)
@TableLogic(value = "0", delval = "1")
@Version
```

### 3. MyBatis-Plus-Ext 的优势

- **无需额外注解**：基于字段类型自动推断数据库字段类型
- **兼容性好**：与 MyBatis-Plus 完全兼容
- **自动建表**：支持 CREATE、UPDATE、ADD 模式
- **字段变动检测**：自动检测实体类变化并同步到数据库

### 4. 字段类型映射

MyBatis-Plus-Ext 会自动将 Java 类型映射为数据库类型：

| Java 类型 | MySQL 类型 |
|-----------|------------|
| String | VARCHAR(255) |
| Integer | INT(11) |
| Long | BIGINT(20) |
| Date | DATETIME |
| Boolean | TINYINT(1) |
| BigDecimal | DECIMAL |

### 5. 启用自动建表

清理完所有实体类后，在 `application.yml` 中启用：

```yaml
mybatis-plus-ext:
  auto-table:
    enable: true
    mode: update  # CREATE/UPDATE/ADD
    show-sql: true
    scan-package: com.shanhai.common.crawler.model.config
```

## 🚀 快速修复建议

由于需要处理的实体类较多，建议：

1. **批量处理**：使用 IDE 的查找替换功能批量移除注解
2. **逐个验证**：每处理一个实体类就编译验证
3. **保留备份**：在修改前备份原始文件

## 📝 示例对比

**修改前（ACTable）：**
```java
@Table(name = "crawler_rule_search")
@TableName("crawler_rule_search")
public class RuleSearch extends BaseEntity {
    @Column(name = "url", comment = "搜索接口URL", type = MySqlTypeConstant.VARCHAR, length = 500, isNull = false)
    private String url;
}
```

**修改后（MyBatis-Plus-Ext）：**
```java
@TableName("crawler_rule_search")
public class RuleSearch extends BaseEntity {
    private String url; // 自动推断为 VARCHAR(255)
}
```

## ✅ 验证步骤

1. 编译通过：`mvn clean compile`
2. 启动应用：检查自动建表日志
3. 验证表结构：确认字段类型正确
4. 测试功能：确保业务功能正常
