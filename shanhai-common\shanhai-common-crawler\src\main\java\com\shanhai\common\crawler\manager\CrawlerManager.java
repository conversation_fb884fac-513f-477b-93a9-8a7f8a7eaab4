package com.shanhai.common.crawler.manager;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.model.NovelBook;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import com.shanhai.common.crawler.service.NovelCrawlerRuleService;
import com.shanhai.common.crawler.service.crawler.NovelCrawlerService;
import com.shanhai.common.crawler.utils.CrawlerValidator;
import com.shanhai.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 爬虫统一管理器
 * <p>
 * 提供统一的爬虫操作入口，整合规则管理和爬虫执行
 * 支持异步执行、批量处理、错误重试等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CrawlerManager {

    private final NovelCrawlerService crawlerService;
    private final NovelCrawlerRuleService ruleService;
    private final CrawlerValidator validator;
    private final ExecutorService executorService;

    /**
     * 构造函数
     *
     * @param crawlerService 爬虫服务
     * @param ruleService 规则服务
     * @param validator 参数验证器
     */
    public CrawlerManager(NovelCrawlerService crawlerService,
                         NovelCrawlerRuleService ruleService,
                         CrawlerValidator validator) {
        this.crawlerService = crawlerService;
        this.ruleService = ruleService;
        this.validator = validator;
        this.executorService = Executors.newFixedThreadPool(10);
    }

    /**
     * 根据站点名称搜索书籍
     *
     * @param sourceName 站点名称
     * @param keyword 搜索关键词
     * @return 书籍列表
     * @throws CrawlerException 参数无效或规则不存在时抛出
     */
    public List<NovelBook> searchBooksBySource(String sourceName, String keyword) {
        // 参数验证
        if (StringUtils.isBlank(sourceName) || StringUtils.isBlank(keyword)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "站点名称和关键词不能为空");
        }

        // 获取爬虫规则
        NovelCrawlerRule rule = getRuleBySourceName(sourceName);

        // 执行搜索
        return crawlerService.searchBooks(rule, keyword);
    }

    /**
     * 多站点并行搜索书籍
     *
     * @param sourceNames 站点名称列表
     * @param keyword 搜索关键词
     * @return 异步返回所有站点的搜索结果合集
     */
    public CompletableFuture<List<NovelBook>> searchBooksParallel(List<String> sourceNames, String keyword) {
        // 参数验证
        if (sourceNames == null || sourceNames.isEmpty() || StringUtils.isBlank(keyword)) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        // 创建并行搜索任务
        List<CompletableFuture<List<NovelBook>>> futures = sourceNames.stream()
                .map(sourceName -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return searchBooksBySource(sourceName, keyword);
                    } catch (Exception e) {
                        log.warn("站点 {} 搜索失败: {}", sourceName, e.getMessage());
                        return Collections.<NovelBook>emptyList();
                    }
                }, executorService))
                .collect(Collectors.toList());

        // 等待所有任务完成并合并结果
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .flatMap(future -> future.join().stream())
                        .collect(Collectors.toList()));
    }

    /**
     * 获取书籍完整信息（包含章节列表）
     */
    public NovelBook getCompleteBookInfo(String sourceName, String bookUrl) {
        NovelCrawlerRule rule = getRuleBySourceName(sourceName);
        
        // 获取书籍基本信息
        NovelBook book = crawlerService.getBookInfo(rule, bookUrl);
        
        // 获取章节列表
        if (StringUtils.isNotBlank(book.getChapterListUrl())) {
            List<NovelBook.NovelChapter> chapters = crawlerService.getChapterList(rule, book.getChapterListUrl());
            book.setChapters(chapters);
        }
        
        return book;
    }

    /**
     * 批量获取章节内容
     */
    public CompletableFuture<List<NovelBook.NovelChapter>> getBatchChapterContentAsync(
            String sourceName, List<String> chapterUrls) {
        
        NovelCrawlerRule rule = getRuleBySourceName(sourceName);
        
        return CompletableFuture.supplyAsync(() -> 
            crawlerService.getBatchChapterContent(rule, chapterUrls), executorService);
    }

    /**
     * 获取所有可用的爬虫站点
     */
    public List<String> getAvailableSources() {
        return ruleService.loadAllRules().stream()
                .map(NovelCrawlerRule::getSourceName)
                .collect(Collectors.toList());
    }

    /**
     * 验证站点规则是否有效
     */
    public boolean validateRule(String sourceName) {
        try {
            NovelCrawlerRule rule = getRuleBySourceName(sourceName);
            validator.validateRule(rule);
            return true;
        } catch (Exception e) {
            log.warn("站点规则验证失败 {}: {}", sourceName, e.getMessage());
            return false;
        }
    }

    /**
     * 根据站点名称获取爬虫规则
     *
     * @param sourceName 站点名称
     * @return 爬虫规则配置
     * @throws CrawlerException 规则不存在时抛出
     */
    private NovelCrawlerRule getRuleBySourceName(String sourceName) {
        return ruleService.loadAllRules().stream()
                .filter(rule -> sourceName.equals(rule.getSourceName()))
                .findFirst()
                .orElseThrow(() -> new CrawlerException(CrawlerErrorCode.RULE_NOT_FOUND, "未找到站点规则: " + sourceName));
    }

    /**
     * 关闭线程池资源
     * 应在应用关闭时调用
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
