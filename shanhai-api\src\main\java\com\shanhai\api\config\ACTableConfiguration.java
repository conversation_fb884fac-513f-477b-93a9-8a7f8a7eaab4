package com.shanhai.api.config;

import com.gitee.sunchenbin.mybatis.actable.manager.handler.StartUpHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import lombok.extern.slf4j.Slf4j;

/**
 * ACTable 配置类
 *
 * 用于加载 ACTable 的配置文件，确保自动建表功能正常工作
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@PropertySource("classpath:actable.properties")
public class ACTableConfiguration implements ApplicationRunner {

    @Autowired(required = false)
    private StartUpHandler startUpHandler;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== ACTable 初始化开始 ===");

        if (startUpHandler != null) {
            log.info("ACTable StartUpHandler 已注入，开始执行建表操作...");
            try {
                startUpHandler.startHandler();
                log.info("ACTable 建表操作完成");
            } catch (Exception e) {
                log.error("ACTable 建表操作失败", e);
            }
        } else {
            log.warn("ACTable StartUpHandler 未注入，可能是ACTable配置有问题");
        }

        log.info("=== ACTable 初始化结束 ===");
    }
}
