package com.shanhai.api.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * ACTable 配置类
 *
 * 用于加载 ACTable 的配置文件，确保自动建表功能正常工作
 *
 * <AUTHOR>
 */
@Configuration
@PropertySource("classpath:actable.properties")
public class ACTableConfiguration {

    // 这个类主要用于加载 actable.properties 配置文件
    // ACTable 会自动读取 mybatis.table.auto 和 mybatis.model.pack 配置

}
