package com.shanhai.common.crawler.service;

import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import java.util.List;

/**
 * 小说爬虫规则聚合管理服务接口
 * <p>
 * 支持多表/配置文件/远程等多数据源聚合的规则管理。
 * 所有方法均为聚合操作，非单表CRUD。
 *
 * <AUTHOR>
 */
public interface NovelCrawlerRuleService {
    /**
     * 聚合查询所有爬虫规则
     * @return 规则列表
     */
    List<CrawlerRuleNovel> loadAllRules();

    /**
     * 按唯一标识查询单个爬虫规则
     * @param id 规则唯一标识（如 sourceId）
     * @return 规则详情
     */
    CrawlerRuleNovel loadRuleById(String id);

    /**
     * 新增聚合爬虫规则
     * @param rule 聚合规则对象
     * @return 是否成功
     */
    boolean saveRule(CrawlerRuleNovel rule);

    /**
     * 更新聚合爬虫规则
     * @param rule 聚合规则对象
     * @return 是否成功
     */
    boolean updateRule(CrawlerRuleNovel rule);

    /**
     * 删除聚合爬虫规则
     * @param id 规则唯一标识
     * @return 是否成功
     */
    boolean deleteRuleById(String id);

    /**
     * 批量导入规则（如JSON文件）
     * @param rules 规则列表
     * @return 是否成功
     */
    boolean importRules(List<CrawlerRuleNovel> rules);

    /**
     * 批量导出所有规则
     * @return 规则列表
     */
    List<CrawlerRuleNovel> exportRules();
} 