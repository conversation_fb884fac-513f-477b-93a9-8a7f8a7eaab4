package com.shanhai.common.crawler.network;

import cn.hutool.core.util.StrUtil;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.model.config.CrawlerRuleNovel;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 统一的HTTP客户端管理器
 * <p>
 * 整合网络请求功能，提供连接池、重试机制、性能监控等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HttpClientManager {
    
    // ================== 配置常量 ==================
    
    private static final int DEFAULT_TIMEOUT = 30000;
    private static final int MAX_RETRY_COUNT = 3;
    private static final long BASE_RETRY_DELAY = 1000L;
    private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    
    // ================== 连接池管理 ==================
    
    private ExecutorService requestExecutor;
    private ScheduledExecutorService retryScheduler;
    
    // ================== 性能监控 ==================
    
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    
    // ================== 限流控制 ==================
    
    private final Map<String, RateLimiter> domainLimiters = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 初始化线程池
        this.requestExecutor = new ThreadPoolExecutor(
            10, 50, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            r -> new Thread(r, "http-client-" + System.currentTimeMillis()),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        this.retryScheduler = Executors.newScheduledThreadPool(5, 
            r -> new Thread(r, "retry-scheduler-" + System.currentTimeMillis()));
        
        log.info("HTTP客户端管理器初始化完成");
    }
    
    @PreDestroy
    public void destroy() {
        if (requestExecutor != null) {
            requestExecutor.shutdown();
        }
        if (retryScheduler != null) {
            retryScheduler.shutdown();
        }
        log.info("HTTP客户端管理器已关闭");
    }
    
    // ================== 核心请求方法 ==================
    
    /**
     * 获取HTML文档（带重试）
     */
    public Document getDocument(String url, CrawlerRuleNovel config) throws CrawlerException {
        return getDocument(url, config, null);
    }
    
    /**
     * 获取HTML文档（带重试和自定义请求头）
     */
    public Document getDocument(String url, CrawlerRuleNovel config, Map<String, String> headers) throws CrawlerException {
        validateRequest(url, config);
        
        long startTime = System.currentTimeMillis();
        totalRequests.incrementAndGet();
        
        try {
            // 应用限流
            applyRateLimit(url);
            
            // 执行请求
            Document result = executeWithRetry(() -> {
                Connection connection = createConnection(url, config, headers);
                return connection.get();
            });
            
            // 记录成功
            successfulRequests.incrementAndGet();
            totalResponseTime.addAndGet(System.currentTimeMillis() - startTime);
            
            log.debug("请求成功: {} ({}ms)", url, System.currentTimeMillis() - startTime);
            return result;
            
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            log.error("请求失败: {} - {}", url, e.getMessage());
            throw convertException(e, url);
        }
    }
    
    /**
     * 获取JSON响应
     */
    public String getJsonResponse(String url, CrawlerRuleNovel config) throws CrawlerException {
        return getJsonResponse(url, config, null);
    }
    
    /**
     * 获取JSON响应（带自定义请求头）
     */
    public String getJsonResponse(String url, CrawlerRuleNovel config, Map<String, String> headers) throws CrawlerException {
        validateRequest(url, config);
        
        long startTime = System.currentTimeMillis();
        totalRequests.incrementAndGet();
        
        try {
            applyRateLimit(url);
            
            String result = executeWithRetry(() -> {
                Connection connection = createConnection(url, config, headers);
                connection.ignoreContentType(true);
                return connection.execute().body();
            });
            
            successfulRequests.incrementAndGet();
            totalResponseTime.addAndGet(System.currentTimeMillis() - startTime);
            
            log.debug("JSON请求成功: {} ({}ms)", url, System.currentTimeMillis() - startTime);
            return result;
            
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            log.error("JSON请求失败: {} - {}", url, e.getMessage());
            throw convertException(e, url);
        }
    }
    
    // ================== 私有辅助方法 ==================
    
    /**
     * 创建连接对象
     */
    private Connection createConnection(String url, CrawlerRuleNovel config, Map<String, String> headers) {
        Connection connection = Jsoup.connect(url)
            .timeout(getTimeout(config))
            .userAgent(getUserAgent(config))
            .followRedirects(true)
            .ignoreHttpErrors(false);
        
        // 应用反爬虫策略
        applyAntiSpiderStrategy(connection, config);
        
        // 添加自定义请求头
        if (headers != null) {
            headers.forEach(connection::header);
        }
        
        // 添加配置中的请求头
        if (config.getHeaders() != null) {
            config.getHeaders().forEach(connection::header);
        }
        
        return connection;
    }
    
    /**
     * 带重试的执行方法
     */
    private <T> T executeWithRetry(Callable<T> callable) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            try {
                return callable.call();
            } catch (Exception e) {
                lastException = e;
                
                if (!isRetryableException(e) || attempt >= MAX_RETRY_COUNT) {
                    break;
                }
                
                long delay = calculateRetryDelay(attempt);
                log.warn("第{}次请求失败，{}ms后重试: {}", attempt, delay, e.getMessage());
                
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new CrawlerException(CrawlerErrorCode.THREAD_INTERRUPTED, "重试被中断");
                }
            }
        }
        
        throw lastException;
    }
    
    /**
     * 应用反爬虫策略
     */
    private void applyAntiSpiderStrategy(Connection connection, CrawlerRuleNovel config) {
        // 设置Cookie
        if (config.getCookies() != null) {
            config.getCookies().forEach(connection::cookie);
        }
        
        // 设置Referer
        if (StrUtil.isNotBlank(config.getSourceUrl())) {
            connection.referrer(config.getSourceUrl());
        }
        
        // 应用延迟
        applyRequestDelay(config);
    }
    
    /**
     * 应用请求延迟
     */
    private void applyRequestDelay(CrawlerRuleNovel config) {
        if (config.getCrawlerRuleAntiSpider() != null) {
            Integer minDelay = config.getCrawlerRuleAntiSpider().getMinDelayMs();
            Integer maxDelay = config.getCrawlerRuleAntiSpider().getMaxDelayMs();
            
            if (minDelay != null && maxDelay != null && minDelay > 0 && maxDelay > minDelay) {
                try {
                    long delay = minDelay + (long) (Math.random() * (maxDelay - minDelay));
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
    
    /**
     * 应用限流
     */
    private void applyRateLimit(String url) {
        String domain = extractDomain(url);
        RateLimiter limiter = domainLimiters.computeIfAbsent(domain, 
            k -> new RateLimiter(10, 1000L)); // 默认每秒10个请求
        
        if (!limiter.tryAcquire()) {
            throw new CrawlerException(CrawlerErrorCode.RATE_LIMIT_EXCEEDED, "请求频率超限: " + domain);
        }
    }
    
    // ================== 工具方法 ==================
    
    private void validateRequest(String url, CrawlerRuleNovel config) {
        if (StrUtil.isBlank(url)) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "URL不能为空");
        }
        if (config == null) {
            throw new CrawlerException(CrawlerErrorCode.INVALID_PARAMETER, "配置不能为空");
        }
    }
    
    private int getTimeout(CrawlerRuleNovel config) {
        return config.getTimeout() != null ? config.getTimeout() : DEFAULT_TIMEOUT;
    }
    
    private String getUserAgent(CrawlerRuleNovel config) {
        return StrUtil.isNotBlank(config.getUserAgent()) ? config.getUserAgent() : DEFAULT_USER_AGENT;
    }
    
    private boolean isRetryableException(Exception e) {
        return e instanceof SocketTimeoutException || 
               e instanceof IOException ||
               (e.getMessage() != null && (
                   e.getMessage().contains("timeout") ||
                   e.getMessage().contains("connection reset") ||
                   e.getMessage().contains("503") ||
                   e.getMessage().contains("502")
               ));
    }
    
    private long calculateRetryDelay(int attempt) {
        return BASE_RETRY_DELAY * (1L << (attempt - 1)); // 指数退避
    }
    
    private CrawlerException convertException(Exception e, String url) {
        if (e instanceof CrawlerException) {
            return (CrawlerException) e;
        }
        
        if (e instanceof SocketTimeoutException) {
            return new CrawlerException(CrawlerErrorCode.NETWORK_TIMEOUT, "请求超时: " + url);
        }
        
        if (e instanceof IOException) {
            String message = e.getMessage();
            if (message != null) {
                if (message.contains("403")) {
                    return new CrawlerException(CrawlerErrorCode.ACCESS_DENIED, "访问被拒绝: " + url);
                } else if (message.contains("404")) {
                    return new CrawlerException(CrawlerErrorCode.BOOK_NOT_FOUND, "页面不存在: " + url);
                }
            }
            return new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "网络请求失败: " + message);
        }
        
        return new CrawlerException(CrawlerErrorCode.SYSTEM_ERROR, "系统异常: " + e.getMessage());
    }
    
    private String extractDomain(String url) {
        try {
            return url.replaceAll("https?://([^/]+).*", "$1");
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    // ================== 性能监控 ==================
    
    /**
     * 获取性能统计信息
     */
    public NetworkStats getStats() {
        long total = totalRequests.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        double avgResponseTime = total > 0 ? (double) totalResponseTime.get() / total : 0.0;
        double successRate = total > 0 ? (double) successful / total * 100 : 0.0;
        
        return NetworkStats.builder()
            .totalRequests(total)
            .successfulRequests(successful)
            .failedRequests(failed)
            .averageResponseTime(avgResponseTime)
            .successRate(successRate)
            .build();
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalResponseTime.set(0);
        log.info("网络请求统计信息已重置");
    }
    
    // ================== 内部类 ==================
    
    /**
     * 简单的限流器实现
     */
    private static class RateLimiter {
        private final int maxRequests;
        private final long windowMs;
        private final AtomicLong requestCount = new AtomicLong(0);
        private volatile long windowStart = System.currentTimeMillis();
        
        public RateLimiter(int maxRequests, long windowMs) {
            this.maxRequests = maxRequests;
            this.windowMs = windowMs;
        }
        
        public boolean tryAcquire() {
            long now = System.currentTimeMillis();
            
            // 检查是否需要重置窗口
            if (now - windowStart >= windowMs) {
                synchronized (this) {
                    if (now - windowStart >= windowMs) {
                        windowStart = now;
                        requestCount.set(0);
                    }
                }
            }
            
            return requestCount.incrementAndGet() <= maxRequests;
        }
    }
}
