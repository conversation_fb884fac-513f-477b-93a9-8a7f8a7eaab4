package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.autotable.annotation.Column;
import lombok.*;

import java.io.Serializable;

/**
 * 书籍信息页解析配置
 * <p>
 * 用于描述如何从书籍详情页提取书籍基本信息。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "crawler_rule_book_info", comment = "书籍信息页解析配置表")
@TableName("crawler_rule_book_info")
public class CrawlerRuleBookInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Column(value = "id", comment = "主键ID", length = 32)
    private String id;

    /**
     * 书名选择器
     */
    @Column(value = "name_selector", comment = "书名选择器", length = 200)
    private String nameSelector;

    /**
     * 作者选择器
     */
    @Column(value = "author_selector", comment = "作者选择器", length = 200)
    private String authorSelector;

    /**
     * 封面图片选择器
     */
    @Column(value = "cover_selector", comment = "封面图片选择器", length = 200)
    private String coverSelector;

    /**
     * 封面图片属性
     */
    @Column(value = "cover_attr", comment = "封面图片属性", length = 50)
    private String coverAttr;

    /**
     * 书籍简介选择器
     */
    @Column(value = "intro_selector", comment = "书籍简介选择器", length = 200)
    private String introSelector;

    /**
     * 书籍状态选择器
     */
    @Column(value = "status_selector", comment = "书籍状态选择器", length = 200)
    private String statusSelector;

    /**
     * 书籍分类选择器
     */
    @Column(value = "category_selector", comment = "书籍分类选择器", length = 200)
    private String categorySelector;

    /**
     * 最新章节选择器
     */
    private String latestChapterSelector;

    /**
     * 更新时间选择器
     */
    private String updateTimeSelector;

    /**
     * 字数选择器
     */
    private String wordCountSelector;

    /**
     * 章节列表页链接选择器
     */
    private String chapterListUrlSelector;

    /**
     * 章节列表页链接属性
     */
    private String chapterListUrlAttr;

    /**
     * 是否需要登录
     */
    @Builder.Default
    private Boolean needLogin = false;

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    private Integer waitTime = 3000;

    private String ruleId;
}
