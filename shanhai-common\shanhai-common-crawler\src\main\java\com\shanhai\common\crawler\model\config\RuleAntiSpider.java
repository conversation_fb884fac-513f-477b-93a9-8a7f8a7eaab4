package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * 反爬虫配置
 * <p>
 * 用于配置反爬虫策略，如延时、代理、User-Agent等。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("crawler_rule_anti_spider")
public class RuleAntiSpider extends BaseEntity implements Serializable {
    /**
     * 最小延时（毫秒）
     */
    @Builder.Default
    private Integer minDelayMs = 1000;

    /**
     * 最大延时（毫秒）
     */
    @Builder.Default
    private Integer maxDelayMs = 3000;

    /**
     * User-Agent列表（JSON格式）
     */
    private String userAgentList;

    /**
     * 代理服务器列表（JSON格式）
     */
    private String proxyList;

    /**
     * 是否启用代理
     */
    @Builder.Default
    private Boolean enableProxy = false;

    /**
     * 是否随机User-Agent
     */
    @Builder.Default
    private Boolean randomUserAgent = true;

    /**
     * 最大重试次数
     */
    @Builder.Default
    private Integer maxRetries = 3;

    /**
     * 超时时间（毫秒）
     */
    @Builder.Default
    private Integer timeout = 30000;

    /**
     * 是否启用Cookie
     */
    @Builder.Default
    private Boolean enableCookie = true;

    /**
     * 自定义请求头（JSON格式）
     */
    private String customHeaders;

    /**
     * 验证码处理策略
     */
    private String captchaStrategy;

    /**
     * 是否启用JavaScript渲染
     */
    @Builder.Default
    private Boolean enableJavaScript = false;

    private String ruleId;
}
