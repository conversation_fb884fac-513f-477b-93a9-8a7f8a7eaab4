package com.shanhai.common.core.utils;

/**
 * 工具类索引
 * 
 * 提供项目中所有工具类的统一入口和说明，避免重复开发
 * 
 * <AUTHOR>
 */
public class UtilsIndex {
    
    /**
     * 工具类使用指南
     * 
     * 核心工具类（shanhai-common-core）：
     * - StringUtils: 字符串处理工具，继承自 Apache Commons Lang
     * - JsonUtils: JSON处理工具，基于 Hutool + Jackson
     * - ValidateUtils: 验证工具，基于 Hutool
     * - ServletUtils: Servlet相关工具
     * 
     * 爬虫专用工具类（shanhai-common-crawler）：
     * - CrawlerUtils: 爬虫通用工具，HTML解析、URL处理等
     * - AntiSpiderUtils: 反爬虫工具，User-Agent、代理设置等
     * - CrawlerValidator: 爬虫配置验证工具
     * - CrawlerCoreManager: 爬虫核心管理器，线程池、缓存等
     * - CrawlerNetworkManager: 网络请求管理器
     * 
     * 使用建议：
     * 1. 优先使用核心工具类中的通用方法
     * 2. 爬虫相关功能使用专用工具类
     * 3. 避免重复造轮子，先检查现有工具类
     * 4. 新增工具方法时，选择合适的工具类添加
     */
    
    private UtilsIndex() {
        // 工具类，禁止实例化
    }
    
    /**
     * 获取字符串工具类
     * 
     * @return StringUtils 实例
     */
    public static Class<StringUtils> getStringUtils() {
        return StringUtils.class;
    }
    
    /**
     * 获取JSON工具类
     * 
     * @return JsonUtils 实例
     */
    public static Class<JsonUtils> getJsonUtils() {
        return JsonUtils.class;
    }
    
    /**
     * 获取验证工具类
     * 
     * @return ValidateUtils 实例
     */
    public static Class<ValidateUtils> getValidateUtils() {
        return ValidateUtils.class;
    }
    
    /**
     * 获取Servlet工具类
     * 
     * @return ServletUtils 实例
     */
    public static Class<ServletUtils> getServletUtils() {
        return ServletUtils.class;
    }
}
