package com.shanhai;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库连接测试
 * 
 * 用于测试数据库连接和表结构
 */
@SpringBootTest
@ActiveProfiles("dev")
public class DatabaseConnectionTest {

    @Autowired(required = false)
    private DataSource dataSource;

    @Test
    public void testDatabaseConnection() {
        if (dataSource == null) {
            System.out.println("❌ DataSource 未注入，请检查数据源配置");
            return;
        }

        try (Connection connection = dataSource.getConnection()) {
            System.out.println("✅ 数据库连接成功");
            
            DatabaseMetaData metaData = connection.getMetaData();
            System.out.println("数据库信息:");
            System.out.println("  - 数据库产品: " + metaData.getDatabaseProductName());
            System.out.println("  - 数据库版本: " + metaData.getDatabaseProductVersion());
            System.out.println("  - 驱动版本: " + metaData.getDriverVersion());
            System.out.println("  - 连接URL: " + metaData.getURL());
            System.out.println("  - 用户名: " + metaData.getUserName());
            
        } catch (Exception e) {
            System.out.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testTableExists() {
        if (dataSource == null) {
            System.out.println("❌ DataSource 未注入，跳过表检查");
            return;
        }

        String[] tables = {
            "crawler_rule",
            "crawler_rule_search", 
            "crawler_rule_book_info",
            "crawler_rule_chapter",
            "crawler_rule_content",
            "crawler_rule_anti_spider",
            "crawler_rule_replace"
        };

        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            System.out.println("检查数据库表结构:");
            for (String tableName : tables) {
                try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                    if (rs.next()) {
                        System.out.println("  ✅ " + tableName + " - 存在");
                    } else {
                        System.out.println("  ❌ " + tableName + " - 不存在");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 表检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCreateTableIfNotExists() {
        if (dataSource == null) {
            System.out.println("❌ DataSource 未注入，跳过建表测试");
            return;
        }

        String createTableSQL = "CREATE TABLE IF NOT EXISTS `test_connection` (" +
                "`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
                "`name` VARCHAR(100) NOT NULL COMMENT '名称'," +
                "`create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
                "PRIMARY KEY (`id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='连接测试表'";

        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            statement.execute(createTableSQL);
            System.out.println("✅ 测试表创建成功");
            
            // 插入测试数据
            statement.execute("INSERT INTO test_connection (name) VALUES ('测试数据')");
            System.out.println("✅ 测试数据插入成功");
            
            // 查询测试数据
            try (ResultSet rs = statement.executeQuery("SELECT * FROM test_connection")) {
                while (rs.next()) {
                    System.out.println("✅ 查询到数据: ID=" + rs.getLong("id") + 
                                     ", Name=" + rs.getString("name") + 
                                     ", CreateTime=" + rs.getTimestamp("create_time"));
                }
            }
            
            // 清理测试表
            statement.execute("DROP TABLE IF EXISTS test_connection");
            System.out.println("✅ 测试表清理完成");
            
        } catch (Exception e) {
            System.out.println("❌ 建表测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
