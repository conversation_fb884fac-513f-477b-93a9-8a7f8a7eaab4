package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import java.io.Serializable;

import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

/**
 * 搜索页解析配置
 * <p>
 * 用于描述如何从搜索页提取书籍列表及相关参数。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("crawler_rule_search")
public class RuleSearch extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索接口URL
     */
    private String url;

    /**
     * 搜索参数模板
     */
    private String searchParam;

    /**
     * 书籍列表选择器
     */
    private String bookListSelector;

    /**
     * 单本书籍项选择器
     */
    private String bookItemSelector;

    /**
     * 书名选择器（CSS/JQ/JSONPath）
     */
    private String nameSelector;

    /**
     * 作者选择器
     */
    private String authorSelector;

    /**
     * 封面图片选择器
     */
    private String coverSelector;

    /**
     * 书籍详情页链接选择器
     */
    private String detailUrlSelector;

    /**
     * 书籍详情页链接属性
     */
    private String detailUrlAttr;

    /**
     * 最新章节选择器
     */
    private String latestChapterSelector;

    /**
     * 书籍状态选择器
     */
    private String statusSelector;

    /**
     * 书籍分类选择器
     */
    private String categorySelector;

    /**
     * 书籍简介选择器
     */
    private String introSelector;

    /**
     * 字数选择器
     */
    private String wordCountSelector;

    /**
     * 更新时间选择器
     */
    private String updateTimeSelector;

    /**
     * 下一页选择器
     */
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    private String nextPageAttr;

    /**
     * 是否启用分页
     */
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    private Integer maxPages = 10;

    /**
     * 请求方法（GET/POST）
     */
    private String method = "GET";

    /**
     * 请求头配置（JSON格式）
     */
    private String headers;

    /**
     * 请求体模板（POST请求时使用）
     */
    private String requestBody;

    /**
     * 编码格式
     */
    private String charset = "UTF-8";

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    private Integer waitTime = 3000;

    private String ruleId;
}
