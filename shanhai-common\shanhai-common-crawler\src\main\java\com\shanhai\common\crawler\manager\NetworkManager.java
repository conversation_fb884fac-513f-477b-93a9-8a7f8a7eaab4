package com.shanhai.common.crawler.manager;

import com.shanhai.common.crawler.exception.CrawlerException;
import com.shanhai.common.crawler.exception.CrawlerErrorCode;
import com.shanhai.common.crawler.model.config.NovelCrawlerRule;
import com.shanhai.common.crawler.utils.AntiSpiderUtils;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 网络请求管理器
 * <p>
 * 统一管理网络请求，提供重试机制、反爬虫处理、连接池管理等功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NetworkManager {

    private static final int DEFAULT_TIMEOUT = 10000;
    private static final int MAX_RETRY_COUNT = 3;
    private static final long BASE_RETRY_DELAY = 1000L;

    /**
     * 获取网页文档
     */
    public Document getDocument(String url, NovelCrawlerRule config) throws CrawlerException {
        return retry(() -> {
            Connection connection = createConnection(url, config);
            try {
                return connection.get();
            } catch (SocketTimeoutException e) {
                throw new CrawlerException(CrawlerErrorCode.NETWORK_TIMEOUT, "请求超时: " + url);
            } catch (IOException e) {
                if (e.getMessage().contains("403")) {
                    throw new CrawlerException(CrawlerErrorCode.ACCESS_DENIED, "访问被拒绝: " + url);
                } else if (e.getMessage().contains("404")) {
                    throw new CrawlerException(CrawlerErrorCode.BOOK_NOT_FOUND, "页面不存在: " + url);
                } else {
                    throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "网络请求失败: " + e.getMessage());
                }
            }
        });
    }

    /**
     * POST请求获取文档
     */
    public Document postDocument(String url, NovelCrawlerRule config, String data) throws CrawlerException {
        return retry(() -> {
            Connection connection = createConnection(url, config);
            try {
                return connection.requestBody(data).post();
            } catch (SocketTimeoutException e) {
                throw new CrawlerException(CrawlerErrorCode.NETWORK_TIMEOUT, "POST请求超时: " + url);
            } catch (IOException e) {
                throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "POST请求失败: " + e.getMessage());
            }
        });
    }

    /**
     * 创建连接对象
     */
    private Connection createConnection(String url, NovelCrawlerRule config) {
        Connection connection = Jsoup.connect(url);
        
        // 设置基本参数
        connection.timeout(config.getTimeout() != null ? config.getTimeout() : DEFAULT_TIMEOUT);
        connection.followRedirects(true);
        connection.ignoreContentType(true);
        connection.ignoreHttpErrors(true);

        // 设置User-Agent
        if (config.getUserAgent() != null) {
            connection.userAgent(config.getUserAgent());
        } else {
            connection.userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        }

        // 设置请求头
        if (config.getHeaders() != null) {
            config.getHeaders().forEach(connection::header);
        }

        // 设置Cookie
        if (config.getCookies() != null) {
            config.getCookies().forEach(connection::cookie);
        }

        // 应用反爬虫策略
        AntiSpiderUtils.apply(connection, config);

        return connection;
    }

    /**
     * 重试机制
     */
    public <T> T retry(Callable<T> callable) throws CrawlerException {
        CrawlerException lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            try {
                return callable.call();
            } catch (CrawlerException e) {
                lastException = e;
                
                // 如果不是可重试的错误，直接抛出
                if (!e.getErrorCode().isRetryable()) {
                    throw e;
                }
                
                if (attempt < MAX_RETRY_COUNT) {
                    long delay = calculateRetryDelay(attempt);
                    log.warn("第{}次请求失败，{}ms后重试: {}", attempt, delay, e.getMessage());
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new CrawlerException(CrawlerErrorCode.THREAD_INTERRUPTED, "重试被中断");
                    }
                } else {
                    log.error("重试{}次后仍然失败: {}", MAX_RETRY_COUNT, e.getMessage());
                }
            } catch (Exception e) {
                throw new CrawlerException(CrawlerErrorCode.SYSTEM_ERROR, "系统异常: " + e.getMessage());
            }
        }
        
        throw lastException;
    }

    /**
     * 计算重试延迟时间（指数退避 + 随机抖动）
     */
    private long calculateRetryDelay(int attempt) {
        long baseDelay = BASE_RETRY_DELAY * (1L << (attempt - 1)); // 指数退避
        long jitter = ThreadLocalRandom.current().nextLong(0, baseDelay / 2); // 随机抖动
        return baseDelay + jitter;
    }

    /**
     * 检查网络连接
     */
    public boolean checkConnection(String url) {
        try {
            Connection connection = Jsoup.connect(url)
                    .timeout(5000)
                    .method(Connection.Method.HEAD);
            
            Connection.Response response = connection.execute();
            return response.statusCode() < 400;
        } catch (Exception e) {
            log.debug("网络连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取页面响应状态码
     */
    public int getStatusCode(String url, NovelCrawlerRule config) throws CrawlerException {
        try {
            Connection connection = createConnection(url, config);
            Connection.Response response = connection.method(Connection.Method.HEAD).execute();
            return response.statusCode();
        } catch (IOException e) {
            throw new CrawlerException(CrawlerErrorCode.NETWORK_ERROR, "获取状态码失败: " + e.getMessage());
        }
    }

    /**
     * 批量检查URL可用性
     */
    public java.util.Map<String, Boolean> batchCheckUrls(java.util.List<String> urls) {
        java.util.Map<String, Boolean> results = new java.util.concurrent.ConcurrentHashMap<>();
        
        urls.parallelStream().forEach(url -> {
            results.put(url, checkConnection(url));
        });
        
        return results;
    }
}
