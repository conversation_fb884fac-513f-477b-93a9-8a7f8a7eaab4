package com.shanhai;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

/**
 * ACTable 自动建表测试
 * 
 * 验证 ACTable 是否能正确创建数据库表
 */
@SpringBootTest
@ActiveProfiles("dev")
public class ACTableTest {

    @Autowired(required = false)
    private DataSource dataSource;

    @Value("${mybatis.table.auto:not-found}")
    private String tableAuto;

    @Value("${mybatis.model.pack:not-found}")
    private String modelPack;

    @Test
    public void testACTableConfiguration() {
        System.out.println("=== ACTable 配置检查 ===");
        System.out.println("mybatis.table.auto: " + tableAuto);
        System.out.println("mybatis.model.pack: " + modelPack);
        
        // 检查配置是否正确
        if ("update".equals(tableAuto)) {
            System.out.println("✅ ACTable 自动建表模式已启用");
        } else {
            System.out.println("❌ ACTable 自动建表模式未启用，当前值: " + tableAuto);
            System.out.println("请将 actable.properties 中的 mybatis.table.auto 设置为 update");
        }
        
        if (modelPack.contains("com.shanhai.service.entity") && 
            modelPack.contains("com.shanhai.common.crawler.model.config")) {
            System.out.println("✅ 实体类包路径配置正确");
        } else {
            System.out.println("❌ 实体类包路径配置可能有问题，当前值: " + modelPack);
        }
    }

    @Test
    public void testDatabaseConnection() {
        System.out.println("=== 数据库连接测试 ===");
        
        if (dataSource == null) {
            System.out.println("❌ DataSource 未注入，请检查数据库配置");
            return;
        }

        try (Connection connection = dataSource.getConnection()) {
            System.out.println("✅ 数据库连接成功");
            System.out.println("数据库URL: " + connection.getMetaData().getURL());
            System.out.println("数据库用户: " + connection.getMetaData().getUserName());
        } catch (Exception e) {
            System.out.println("❌ 数据库连接失败: " + e.getMessage());
        }
    }

    @Test
    public void testTablesExist() {
        System.out.println("=== 数据库表检查 ===");
        
        if (dataSource == null) {
            System.out.println("❌ DataSource 未注入，跳过表检查");
            return;
        }

        String[] expectedTables = {
            "crawler_rule",
            "crawler_rule_search",
            "crawler_rule_book_info",
            "crawler_rule_chapter",
            "crawler_rule_content",
            "crawler_rule_anti_spider",
            "crawler_rule_replace",
            "test_entity"
        };

        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            System.out.println("检查预期的数据库表:");
            int existingTables = 0;
            
            for (String tableName : expectedTables) {
                try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                    if (rs.next()) {
                        System.out.println("  ✅ " + tableName + " - 存在");
                        existingTables++;
                    } else {
                        System.out.println("  ❌ " + tableName + " - 不存在");
                    }
                }
            }
            
            System.out.println();
            System.out.println("表统计: " + existingTables + "/" + expectedTables.length + " 个表存在");
            
            if (existingTables == 0) {
                System.out.println("⚠️  没有找到任何预期的表，可能的原因:");
                System.out.println("   1. ACTable 配置未生效");
                System.out.println("   2. 实体类注解有问题");
                System.out.println("   3. 数据库连接有问题");
                System.out.println("   4. 应用启动时出现错误");
            } else if (existingTables < expectedTables.length) {
                System.out.println("⚠️  部分表缺失，请检查:");
                System.out.println("   1. 对应实体类的 @Table 和 @Column 注解");
                System.out.println("   2. 实体类是否在配置的包路径中");
                System.out.println("   3. 启动日志中是否有错误信息");
            } else {
                System.out.println("✅ 所有预期的表都存在，ACTable 工作正常");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 表检查失败: " + e.getMessage());
        }
    }

    @Test
    public void testRecommendations() {
        System.out.println("=== 建议和说明 ===");
        System.out.println("1. 确保 actable.properties 中 mybatis.table.auto=update");
        System.out.println("2. 确保所有实体类都有正确的 @Table 和 @Column 注解");
        System.out.println("3. 确保实体类在配置的包路径中");
        System.out.println("4. 如果表仍未创建，请检查启动日志中的错误信息");
        System.out.println("5. 可以尝试将 mybatis.table.auto 设置为 create（注意：会删除现有数据）");
        System.out.println();
        System.out.println("ACTable 支持的模式:");
        System.out.println("  - none: 不执行任何操作");
        System.out.println("  - update: 更新表结构（推荐，不会删除数据）");
        System.out.println("  - create: 每次启动都重新创建表（会删除原有数据）");
        System.out.println("  - add: 只新增字段和表，不删除和修改");
    }
}
