package com.shanhai.common.crawler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shanhai.common.crawler.model.config.*;
import com.shanhai.common.crawler.repository.NovelCrawlerRuleMapper;
import com.shanhai.common.crawler.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class NovelCrawlerRuleServiceImpl
        extends ServiceImpl<NovelCrawlerRuleMapper, NovelCrawlerRule>
        implements NovelCrawlerRuleService {
    private final RuleSearchService ruleSearchService;
    private final RuleBookInfoService ruleBookInfoService;
    private final ChapterListService chapterListService;
    private final RuleContentService ruleContentService;
    private final AntiSpiderService antiSpiderService;

    public NovelCrawlerRuleServiceImpl(
            RuleSearchService ruleSearchService,
            RuleBookInfoService ruleBookInfoService,
            ChapterListService chapterListService,
            RuleContentService ruleContentService,
            AntiSpiderService antiSpiderService) {
        this.ruleSearchService = ruleSearchService;
        this.ruleBookInfoService = ruleBookInfoService;
        this.chapterListService = chapterListService;
        this.ruleContentService = ruleContentService;
        this.antiSpiderService = antiSpiderService;
    }

    @Override
    @Transactional
    public boolean saveRule(NovelCrawlerRule rule) {
        this.save(rule); // 使用 MyBatis-Plus 的 save
        String ruleId = rule.getId();
        if (rule.getRuleSearch() != null) {
            rule.getRuleSearch().setRuleId(ruleId);
            ruleSearchService.save(rule.getRuleSearch());
        }
        if (rule.getRuleBookInfo() != null) {
            rule.getRuleBookInfo().setRuleId(ruleId);
            ruleBookInfoService.save(rule.getRuleBookInfo());
        }
        if (rule.getRuleChapter() != null) {
            rule.getRuleChapter().setRuleId(ruleId);
            chapterListService.save(rule.getRuleChapter());
        }
        if (rule.getRuleContent() != null) {
            rule.getRuleContent().setRuleId(ruleId);
            ruleContentService.save(rule.getRuleContent());
        }
        if (rule.getRuleAntiSpider() != null) {
            rule.getRuleAntiSpider().setRuleId(ruleId);
            antiSpiderService.save(rule.getRuleAntiSpider());
        }
        return true;
    }

    @Override
    public NovelCrawlerRule loadRuleById(String id) {
        NovelCrawlerRule rule = this.getById(id);
        if (rule == null) return null;
        rule.setRuleSearch(ruleSearchService.getOne(new LambdaQueryWrapper<RuleSearch>().eq(RuleSearch::getRuleId, id)));
        rule.setRuleBookInfo(ruleBookInfoService.getOne(new LambdaQueryWrapper<RuleBookInfo>().eq(RuleBookInfo::getRuleId, id)));
        rule.setRuleChapter(chapterListService.getOne(new LambdaQueryWrapper<RuleChapter>().eq(RuleChapter::getRuleId, id)));
        rule.setRuleContent(ruleContentService.getOne(new LambdaQueryWrapper<RuleContent>().eq(RuleContent::getRuleId, id)));
        rule.setRuleAntiSpider(antiSpiderService.getOne(new LambdaQueryWrapper<RuleAntiSpider>().eq(RuleAntiSpider::getRuleId, id)));
        return rule;
    }

    @Override
    public List<NovelCrawlerRule> loadAllRules() {
        List<NovelCrawlerRule> rules = this.list();
        for (NovelCrawlerRule rule : rules) {
            String id = rule.getId();
            rule.setRuleSearch(ruleSearchService.getOne(new LambdaQueryWrapper<RuleSearch>().eq(RuleSearch::getRuleId, id)));
            rule.setRuleBookInfo(ruleBookInfoService.getOne(new LambdaQueryWrapper<RuleBookInfo>().eq(RuleBookInfo::getRuleId, id)));
            rule.setRuleChapter(chapterListService.getOne(new LambdaQueryWrapper<RuleChapter>().eq(RuleChapter::getRuleId, id)));
            rule.setRuleContent(ruleContentService.getOne(new LambdaQueryWrapper<RuleContent>().eq(RuleContent::getRuleId, id)));
            rule.setRuleAntiSpider(antiSpiderService.getOne(new LambdaQueryWrapper<RuleAntiSpider>().eq(RuleAntiSpider::getRuleId, id)));
        }
        return rules;
    }

    @Override
    @Transactional
    public boolean updateRule(NovelCrawlerRule rule) {
        this.updateById(rule);
        String ruleId = rule.getId();
        if (rule.getRuleSearch() != null) {
            ruleSearchService.update(rule.getRuleSearch(), new LambdaQueryWrapper<RuleSearch>().eq(RuleSearch::getRuleId, ruleId));
        }
        if (rule.getRuleBookInfo() != null) {
            ruleBookInfoService.update(rule.getRuleBookInfo(), new LambdaQueryWrapper<RuleBookInfo>().eq(RuleBookInfo::getRuleId, ruleId));
        }
        if (rule.getRuleChapter() != null) {
            chapterListService.update(rule.getRuleChapter(), new LambdaQueryWrapper<RuleChapter>().eq(RuleChapter::getRuleId, ruleId));
        }
        if (rule.getRuleContent() != null) {
            ruleContentService.update(rule.getRuleContent(), new LambdaQueryWrapper<RuleContent>().eq(RuleContent::getRuleId, ruleId));
        }
        if (rule.getRuleAntiSpider() != null) {
            antiSpiderService.update(rule.getRuleAntiSpider(), new LambdaQueryWrapper<RuleAntiSpider>().eq(RuleAntiSpider::getRuleId, ruleId));
        }
        return true;
    }

    @Override
    @Transactional
    public boolean deleteRuleById(String id) {
        ruleSearchService.remove(new LambdaQueryWrapper<RuleSearch>().eq(RuleSearch::getRuleId, id));
        ruleBookInfoService.remove(new LambdaQueryWrapper<RuleBookInfo>().eq(RuleBookInfo::getRuleId, id));
        chapterListService.remove(new LambdaQueryWrapper<RuleChapter>().eq(RuleChapter::getRuleId, id));
        ruleContentService.remove(new LambdaQueryWrapper<RuleContent>().eq(RuleContent::getRuleId, id));
        antiSpiderService.remove(new LambdaQueryWrapper<RuleAntiSpider>().eq(RuleAntiSpider::getRuleId, id));
        this.removeById(id);
        return true;
    }

    @Override
    public List<NovelCrawlerRule> exportRules() {
        return loadAllRules();
    }

    @Override
    @Transactional
    public boolean importRules(List<NovelCrawlerRule> rules) {
        for (NovelCrawlerRule rule : rules) {
            this.saveRule(rule);
        }
        return true;
    }
} 