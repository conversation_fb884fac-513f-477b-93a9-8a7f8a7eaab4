package com.shanhai.common.crawler.model.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.shanhai.common.core.model.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * 章节列表页解析配置
 * <p>
 * 用于描述如何从章节列表页提取章节信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("crawler_rule_chapter")
public class RuleChapter extends BaseEntity implements Serializable {
    /**
     * 章节列表选择器
     */
    private String chapterListSelector;

    /**
     * 单个章节项选择器
     */
    private String chapterItemSelector;

    /**
     * 章节标题选择器
     */
    private String titleSelector;

    /**
     * 章节链接选择器
     */
    private String urlSelector;

    /**
     * 章节链接属性
     */
    private String urlAttr;

    /**
     * 章节发布时间选择器
     */
    private String publishTimeSelector;

    /**
     * 是否反向排序
     */
    @Builder.Default
    private Boolean reverseOrder = false;

    /**
     * 下一页选择器
     */
    private String nextPageSelector;

    /**
     * 下一页链接属性
     */
    private String nextPageAttr;

    /**
     * 是否启用分页
     */
    @Builder.Default
    private Boolean enablePagination = false;

    /**
     * 最大页数限制
     */
    @Builder.Default
    private Integer maxPages = 50;

    /**
     * 等待加载的选择器（动态页面）
     */
    private String waitForSelector;

    /**
     * 等待时间（毫秒）
     */
    @Builder.Default
    private Integer waitTime = 3000;

    private String ruleId;
}
